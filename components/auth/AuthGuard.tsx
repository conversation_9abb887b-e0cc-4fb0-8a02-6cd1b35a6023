"use client";

import React, { useEffect, useState } from "react";
import { UserType } from "@/lib/auth/types";

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  allowedUserTypes?: UserType[];
  requireTenant?: boolean;
  redirectTo?: string;
  loadingComponent?: React.ReactNode;
  unauthorizedComponent?: React.ReactNode;
}

interface AuthState {
  isLoading: boolean;
  isAuthenticated: boolean;
  user?: {
    id: string;
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    userType: UserType;
    tenantId: string;
    isActive: boolean;
  };
  error?: string;
}

/**
 * AuthGuard Component
 * 
 * Client-side authentication guard that validates user sessions
 * and controls access to protected components/pages.
 * 
 * Requirements: 3.4, 3.6, 8.1, 8.2
 */
export function AuthGuard({
  children,
  requireAuth = true,
  allowedUserTypes = [],
  requireTenant = true,
  redirectTo = "/auth/login",
  loadingComponent,
  unauthorizedComponent,
}: AuthGuardProps) {
  const [authState, setAuthState] = useState<AuthState>({
    isLoading: true,
    isAuthenticated: false,
  });

  useEffect(() => {
    validateAuth();
  }, []);

  const validateAuth = async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));

      // Check authentication status by calling a validation endpoint
      const response = await fetch('/api/auth/validate', {
        method: 'GET',
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        
        if (result.success && result.user) {
          setAuthState({
            isLoading: false,
            isAuthenticated: true,
            user: result.user,
          });
        } else {
          setAuthState({
            isLoading: false,
            isAuthenticated: false,
            error: result.message || 'Authentication failed',
          });
        }
      } else {
        setAuthState({
          isLoading: false,
          isAuthenticated: false,
          error: 'Authentication check failed',
        });
      }
    } catch (error) {
      console.error('Auth validation error:', error);
      setAuthState({
        isLoading: false,
        isAuthenticated: false,
        error: 'Network error during authentication',
      });
    }
  };

  // Show loading state
  if (authState.isLoading) {
    return loadingComponent || <DefaultLoadingComponent />;
  }

  // Handle unauthenticated users
  if (requireAuth && !authState.isAuthenticated) {
    if (typeof window !== 'undefined') {
      // Client-side redirect
      const currentPath = window.location.pathname;
      const redirectUrl = `${redirectTo}?redirect=${encodeURIComponent(currentPath)}`;
      window.location.href = redirectUrl;
      return loadingComponent || <DefaultLoadingComponent />;
    }
    return unauthorizedComponent || <DefaultUnauthorizedComponent redirectTo={redirectTo} />;
  }

  // Handle user type authorization
  if (requireAuth && authState.user && allowedUserTypes.length > 0) {
    const hasPermission = allowedUserTypes.includes(authState.user.userType);
    
    if (!hasPermission) {
      return unauthorizedComponent || <DefaultUnauthorizedComponent 
        message="You don't have permission to access this resource."
        redirectTo="/dashboard"
      />;
    }
  }

  // Handle tenant requirement
  if (requireTenant && authState.user && !authState.user.tenantId) {
    return unauthorizedComponent || <DefaultUnauthorizedComponent 
      message="No clinic association found. Please contact your administrator."
      redirectTo="/auth/login"
    />;
  }

  // User is authenticated and authorized
  return <>{children}</>;
}

/**
 * Default Loading Component
 */
function DefaultLoadingComponent() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="text-center space-y-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
        <p className="text-sm text-muted-foreground">Verifying authentication...</p>
      </div>
    </div>
  );
}

/**
 * Default Unauthorized Component
 */
function DefaultUnauthorizedComponent({ 
  message = "You need to be logged in to access this page.",
  redirectTo = "/auth/login"
}: {
  message?: string;
  redirectTo?: string;
}) {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="text-center space-y-4 max-w-md mx-auto px-4">
        <div className="text-6xl">🔒</div>
        <h1 className="text-2xl font-bold">Access Restricted</h1>
        <p className="text-muted-foreground">{message}</p>
        <a
          href={redirectTo}
          className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
        >
          Go to Login
        </a>
      </div>
    </div>
  );
}

/**
 * Hook to access current auth state
 */
export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    isLoading: true,
    isAuthenticated: false,
  });

  useEffect(() => {
    const validateAuth = async () => {
      try {
        const response = await fetch('/api/auth/validate', {
          method: 'GET',
          credentials: 'include',
        });

        if (response.ok) {
          const result = await response.json();
          
          if (result.success && result.user) {
            setAuthState({
              isLoading: false,
              isAuthenticated: true,
              user: result.user,
            });
          } else {
            setAuthState({
              isLoading: false,
              isAuthenticated: false,
              error: result.message || 'Authentication failed',
            });
          }
        } else {
          setAuthState({
            isLoading: false,
            isAuthenticated: false,
            error: 'Authentication check failed',
          });
        }
      } catch (error) {
        console.error('Auth validation error:', error);
        setAuthState({
          isLoading: false,
          isAuthenticated: false,
          error: 'Network error during authentication',
        });
      }
    };

    validateAuth();
  }, []);

  return authState;
}