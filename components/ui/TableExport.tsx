"use client"

import * as React from "react"
import { Button } from "@/components/ui/button"
import { LoadingSpinner } from "@/components/ui/LoadingSpinner"
import { ColumnDef } from "@/components/ui/DataTable"
import { cn } from "@/lib/utils"

export interface TableExportProps<T> {
  data: T[]
  columns: ColumnDef<T>[]
  fileName?: string
  className?: string
}

export function TableExport<T>({ data, columns, fileName = "export", className }: TableExportProps<T>) {
  const [exporting, setExporting] = React.useState<"csv" | "pdf" | null>(null)

  const getHeaderLabel = (col: ColumnDef<T>, index: number): string => {
    if (typeof col.header === "string") return col.header
    if (col.id) return col.id
    return `Column ${index + 1}`
  }

  const getCellValue = (row: T, col: ColumnDef<T>): string | number | null => {
    if (typeof col.accessor === "function") return sanitize(col.accessor(row))
    if (typeof col.accessor === "string") return sanitize((row as any)[col.accessor])
    if (col.cell) return sanitize(col.cell(row) as any)
    return ""
  }

  const sanitize = (value: unknown): string => {
    if (value == null) return ""
    if (typeof value === "string" || typeof value === "number" || typeof value === "boolean") return String(value)
    return ""
  }

  const exportCSV = async () => {
    try {
      setExporting("csv")
      const headers = columns.map((c, i) => escapeCsv(getHeaderLabel(c, i))).join(",")
      const rows = data.map((row) =>
        columns
          .map((c) => escapeCsv(getCellValue(row, c) ?? ""))
          .join(",")
      )
      const csv = [headers, ...rows].join("\n")
      const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" })
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `${fileName}.csv`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } finally {
      setExporting(null)
    }
  }

  const escapeCsv = (value: string | number): string => {
    const str = String(value)
    if (/[",\n]/.test(str)) {
      return '"' + str.replace(/"/g, '""') + '"'
    }
    return str
  }

  const exportPDF = async () => {
    try {
      setExporting("pdf")
      const win = window.open("", "_blank")
      if (!win) return
      const doc = win.document
      doc.open()
      const style = `
        * { box-sizing: border-box; }
        body { font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif; padding: 24px; }
        h1 { font-size: 16px; margin: 0 0 12px; }
        table { border-collapse: collapse; width: 100%; font-size: 12px; }
        th, td { border: 1px solid #e5e7eb; padding: 8px; text-align: left; }
        th { background: #f3f4f6; }
      `
      const thead = `<tr>${columns
        .map((c, i) => `<th>${escapeHtml(getHeaderLabel(c, i))}</th>`)
        .join("")}</tr>`
      const tbody = data
        .map((row) =>
          `<tr>${columns
            .map((c) => `<td>${escapeHtml(getCellValue(row, c) ?? "")}</td>`)
            .join("")}</tr>`
        )
        .join("")
      const html = `<!doctype html><html><head><meta charset="utf-8" /><title>${escapeHtml(
        fileName
      )}</title><style>${style}</style></head><body><h1>${escapeHtml(
        fileName
      )}</h1><table><thead>${thead}</thead><tbody>${tbody}</tbody></table><script>window.onload = () => { window.print(); };</script></body></html>`
      doc.write(html)
      doc.close()
    } finally {
      setExporting(null)
    }
  }

  const escapeHtml = (value: string | number): string => {
    return String(value)
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/\"/g, "&quot;")
      .replace(/'/g, "&#039;")
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Button onClick={exportCSV} disabled={!!exporting}>
        {exporting === "csv" ? <LoadingSpinner size="sm" label="Exporting CSV" /> : "Export CSV"}
      </Button>
      <Button variant="outline" onClick={exportPDF} disabled={!!exporting}>
        {exporting === "pdf" ? <LoadingSpinner size="sm" label="Exporting PDF" /> : "Export PDF"}
      </Button>
    </div>
  )
}
