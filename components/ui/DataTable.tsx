"use client"

import * as React from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { cn } from "@/lib/utils"
import { ChevronsUpDown, ChevronUp, ChevronDown, ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export type ColumnDef<T> = {
  id?: string
  header: React.ReactNode | ((col: ColumnDef<T>) => React.ReactNode)
  accessor?: keyof T | ((row: T) => React.ReactNode)
  cell?: (row: T) => React.ReactNode
  className?: string
  sortable?: boolean
  sortAccessor?: (row: T) => string | number
}

export type SortDirection = "asc" | "desc"

export interface DataTableProps<T> {
  data: T[]
  columns: ColumnDef<T>[]
  className?: string
  emptyState?: React.ReactNode
  rowKey?: (row: T, index: number) => React.Key
  defaultSort?: { columnId?: string; direction: SortDirection }
  onSortChange?: (columnId: string, direction: SortDirection) => void
  responsive?: boolean
  showColumnControls?: boolean
  initialVisibleColumnIds?: string[]
  visibleColumnIds?: string[]
  onVisibleColumnIdsChange?: (visible: string[]) => void
  columnOrder?: string[]
  onColumnOrderChange?: (order: string[]) => void
  enableBulkSelection?: boolean
  selectedRowKeys?: React.Key[]
  onSelectedRowKeysChange?: (keys: React.Key[]) => void
  bulkActions?: React.ReactNode
}

export function DataTable<T>({
  data,
  columns,
  className,
  emptyState,
  rowKey,
  defaultSort,
  onSortChange,
  responsive = true,
  showColumnControls = false,
  initialVisibleColumnIds,
  visibleColumnIds: visibleColumnIdsProp,
  onVisibleColumnIdsChange,
  columnOrder: columnOrderProp,
  onColumnOrderChange,
  enableBulkSelection = false,
  selectedRowKeys: selectedRowKeysProp,
  onSelectedRowKeysChange,
  bulkActions,
}: DataTableProps<T>) {
  const initialSortIndex = React.useMemo(() => {
    if (!defaultSort?.columnId) return -1
    return columns.findIndex((c) => (c.id ?? "") === defaultSort.columnId)
  }, [columns, defaultSort?.columnId])

  const [sortIndex, setSortIndex] = React.useState<number>(initialSortIndex)
  const [sortDir, setSortDir] = React.useState<SortDirection>(defaultSort?.direction ?? "asc")

  const getSortValue = React.useCallback(
    (row: T, col: ColumnDef<T>): string | number | null => {
      if (col.sortAccessor) return col.sortAccessor(row)
      if (typeof col.accessor === "string") return (row[col.accessor] as unknown) as string | number
      return null
    },
    []
  )

  const sortedData = React.useMemo(() => {
    if (!data || data.length === 0) return [] as T[]
    if (sortIndex < 0) return data

    const col = columns[sortIndex]
    if (!col) return data

    const copy = [...data]
    copy.sort((a, b) => {
      const va = getSortValue(a, col)
      const vb = getSortValue(b, col)

      if (va == null && vb == null) return 0
      if (va == null) return sortDir === "asc" ? -1 : 1
      if (vb == null) return sortDir === "asc" ? 1 : -1

      if (typeof va === "number" && typeof vb === "number") {
        return sortDir === "asc" ? va - vb : vb - va
      }

      const sa = String(va).toLowerCase()
      const sb = String(vb).toLowerCase()
      if (sa < sb) return sortDir === "asc" ? -1 : 1
      if (sa > sb) return sortDir === "asc" ? 1 : -1
      return 0
    })
    return copy
  }, [columns, data, getSortValue, sortDir, sortIndex])

  // Column visibility and order state
  const allColumnIds = React.useMemo(
    () => columns.map((c, i) => c.id ?? String(i)),
    [columns]
  )

  const [internalVisible, setInternalVisible] = React.useState<string[]>(
    initialVisibleColumnIds && initialVisibleColumnIds.length > 0
      ? initialVisibleColumnIds
      : allColumnIds
  )
  const visibleColumnIds = visibleColumnIdsProp ?? internalVisible

  const setVisibleColumnIds = React.useCallback(
    (next: string[]) => {
      if (onVisibleColumnIdsChange) onVisibleColumnIdsChange(next)
      else setInternalVisible(next)
    },
    [onVisibleColumnIdsChange]
  )

  const [internalOrder, setInternalOrder] = React.useState<string[]>(allColumnIds)
  const order = columnOrderProp ?? internalOrder
  const setOrder = React.useCallback(
    (next: string[]) => {
      if (onColumnOrderChange) onColumnOrderChange(next)
      else setInternalOrder(next)
    },
    [onColumnOrderChange]
  )

  React.useEffect(() => {
    setInternalOrder(allColumnIds)
    if (!initialVisibleColumnIds) setInternalVisible(allColumnIds)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [allColumnIds.join("|")])

  const orderedColumns = React.useMemo(() => {
    const idToCol = columns.map((c, i) => ({ id: c.id ?? String(i), col: c }))
      .reduce<Record<string, ColumnDef<T>>>((acc, cur) => {
        acc[cur.id] = cur.col
        return acc
      }, {})
    const arranged = order.map((id) => idToCol[id]).filter(Boolean)
    return arranged.filter((c, i) => {
      const id = order[i]
      return visibleColumnIds.includes(id)
    })
  }, [columns, order, visibleColumnIds])

  const renderCell = React.useCallback((row: T, col: ColumnDef<T>) => {
    return col.cell
      ? col.cell(row)
      : typeof col.accessor === "function"
      ? col.accessor(row)
      : typeof col.accessor === "string"
      ? String((row as any)[col.accessor] ?? "")
      : null
  }, [])

  const moveColumn = (id: string, direction: -1 | 1) => {
    const idx = order.indexOf(id)
    if (idx < 0) return
    const swapWith = idx + direction
    if (swapWith < 0 || swapWith >= order.length) return
    const next = [...order]
    ;[next[idx], next[swapWith]] = [next[swapWith], next[idx]]
    setOrder(next)
  }

  // Bulk selection state
  const computeRowKey = React.useCallback(
    (row: T, index: number) => (rowKey ? rowKey(row, index) : index),
    [rowKey]
  )
  const [internalSelected, setInternalSelected] = React.useState<Set<React.Key>>(new Set())
  const selectedSet = React.useMemo(
    () => new Set(selectedRowKeysProp ?? Array.from(internalSelected)),
    [selectedRowKeysProp, internalSelected]
  )
  const setSelectedKeys = React.useCallback(
    (next: Set<React.Key>) => {
      if (onSelectedRowKeysChange) onSelectedRowKeysChange(Array.from(next))
      else setInternalSelected(next)
    },
    [onSelectedRowKeysChange]
  )
  const allVisibleKeys = React.useMemo(
    () => (sortedData as T[]).map((r, i) => computeRowKey(r, i)),
    [sortedData, computeRowKey]
  )
  const allSelected = enableBulkSelection && allVisibleKeys.every((k) => selectedSet.has(k)) && allVisibleKeys.length > 0
  const toggleAll = () => {
    if (!enableBulkSelection) return
    const next = new Set<React.Key>(selectedSet)
    if (allSelected) {
      allVisibleKeys.forEach((k) => next.delete(k))
    } else {
      allVisibleKeys.forEach((k) => next.add(k))
    }
    setSelectedKeys(next)
  }
  const toggleOne = (key: React.Key) => {
    if (!enableBulkSelection) return
    const next = new Set<React.Key>(selectedSet)
    if (next.has(key)) next.delete(key)
    else next.add(key)
    setSelectedKeys(next)
  }

  const toggleColumn = (id: string, checked: boolean) => {
    if (checked) {
      if (!visibleColumnIds.includes(id)) setVisibleColumnIds([...visibleColumnIds, id])
    } else {
      setVisibleColumnIds(visibleColumnIds.filter((c) => c !== id))
    }
  }

  const handleHeaderClick = (index: number, col: ColumnDef<T>) => {
    if (!col.sortable) return
    if (sortIndex === index) {
      const nextDir: SortDirection = sortDir === "asc" ? "desc" : "asc"
      setSortDir(nextDir)
      if (onSortChange) onSortChange(col.id ?? String(index), nextDir)
    } else {
      setSortIndex(index)
      setSortDir("asc")
      if (onSortChange) onSortChange(col.id ?? String(index), "asc")
    }
  }

  if (!data || data.length === 0) {
    return emptyState ? <>{emptyState}</> : null
  }



  return (
    <div className={cn("w-full", className)}>
      {showColumnControls ? (
        <div className="mb-2 flex items-center justify-end">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">Columns</Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {allColumnIds.map((id, i) => {
                const col = columns[i]
                const header = typeof col.header === "function" ? col.header(col) : col.header
                const isChecked = visibleColumnIds.includes(id)
                return (
                  <div key={id} className="flex items-center justify-between gap-2 px-1">
                    <DropdownMenuCheckboxItem
                      checked={isChecked}
                      onCheckedChange={(v) => toggleColumn(id, Boolean(v))}
                      className="pr-0"
                    >
                      <span className="truncate">
                        {typeof header === "string" ? header : (header as any) ?? `Column ${i+1}`}
                      </span>
                    </DropdownMenuCheckboxItem>
                    <div className="flex items-center gap-1">
                      <Button variant="ghost" size="icon" className="h-7 w-7" onClick={() => moveColumn(id, -1)}>
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-7 w-7" onClick={() => moveColumn(id, 1)}>
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )
              })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ) : null}
      {/* Table layout for md+ screens */}
      <div className={cn(responsive ? "hidden md:block" : "block")}> 
        <Table>
      <TableHeader>
        <TableRow>
          {enableBulkSelection ? (
            <TableHead className="w-9">
              <input aria-label="Select all rows" type="checkbox" checked={allSelected} onChange={toggleAll} />
            </TableHead>
          ) : null}
          {orderedColumns.map((col) => {
            const i = columns.findIndex((c) => (c.id ?? "") === (col.id ?? ""))
            const isActive = i === sortIndex
            const ariaSort = col.sortable ? (isActive ? (sortDir === "asc" ? "ascending" : "descending") : "none") : undefined
            return (
              <TableHead
                key={col.id ?? String(i)}
                className={cn(col.className, col.sortable && "cursor-pointer select-none")}
                aria-sort={ariaSort as React.AriaAttributes["aria-sort"]}
                onClick={() => handleHeaderClick(i, col)}
              >
                <span className="inline-flex items-center gap-1">
                  {typeof col.header === "function" ? col.header(col) : col.header}
                  {col.sortable && (
                    isActive ? (
                      sortDir === "asc" ? (
                        <ChevronUp className="h-3.5 w-3.5" />
                      ) : (
                        <ChevronDown className="h-3.5 w-3.5" />
                      )
                    ) : (
                      <ChevronsUpDown className="h-3.5 w-3.5 text-muted-foreground" />
                    )
                  )}
                </span>
              </TableHead>
            )
          })}
        </TableRow>
      </TableHeader>
      <TableBody>
        {(sortedData as T[]).map((row, rowIndex) => {
          const key = computeRowKey(row, rowIndex)
          const isSelected = enableBulkSelection && selectedSet.has(key)
          return (
          <TableRow key={key} data-state={isSelected ? "selected" : undefined}>
            {enableBulkSelection ? (
              <TableCell className="w-9">
                <input aria-label={`Select row ${rowIndex + 1}`} type="checkbox" checked={isSelected} onChange={() => toggleOne(key)} />
              </TableCell>
            ) : null}
            {orderedColumns.map((col, colIndex) => (
              <TableCell key={(col.id ?? colIndex) as React.Key} className={col.className}>
                {renderCell(row, col)}
              </TableCell>
            ))}
          </TableRow>
        )})}
      </TableBody>
        </Table>
      </div>

      {/* Card layout for small screens */}
      {responsive ? (
        <div className="md:hidden space-y-2">
          {(sortedData as T[]).map((row, rowIndex) => (
            <div
              key={rowKey ? rowKey(row, rowIndex) : rowIndex}
              className="rounded-lg border bg-card text-card-foreground p-3"
              role="group"
              aria-label="Row"
            >
              <div className="grid grid-cols-2 gap-x-4 gap-y-1">
                {orderedColumns.map((col, colIndex) => (
                  <React.Fragment key={(col.id ?? colIndex) as React.Key}>
                    <div className="text-xs text-muted-foreground">
                      {typeof col.header === "function" ? col.header(col) : col.header}
                    </div>
                    <div className="text-sm">
                      {renderCell(row, col)}
                    </div>
                  </React.Fragment>
                ))}
              </div>
            </div>
          ))}
        </div>
      ) : null}

      {enableBulkSelection && bulkActions ? (
        <div className="mt-2 flex items-center gap-2">
          {bulkActions}
        </div>
      ) : null}
    </div>
  )
}
