"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { Users, ClipboardList, FileWarning } from "lucide-react"

export interface DashboardMetricsData {
  todayPatients: number
  pendingTreatments: number
  outstandingInvoices: number
}

export interface DashboardMetricsProps {
  metrics: DashboardMetricsData
  loading?: boolean
  className?: string
}

export function DashboardMetrics({ metrics, loading = false, className }: DashboardMetricsProps) {
  const items = [
    {
      label: "Today's Patients",
      value: metrics.todayPatients,
      icon: Users,
    },
    {
      label: "Pending Treatments",
      value: metrics.pendingTreatments,
      icon: ClipboardList,
    },
    {
      label: "Outstanding Invoices",
      value: metrics.outstandingInvoices,
      icon: FileWarning,
    },
  ]

  return (
    <div className={cn("grid gap-4 sm:grid-cols-2 lg:grid-cols-3", className)}>
      {items.map((item) => (
        <MetricCard key={item.label} label={item.label} value={item.value} icon={item.icon} loading={loading} />)
      )}
    </div>
  )
}

function MetricCard({
  label,
  value,
  icon: Icon,
  loading,
}: {
  label: string
  value: number
  icon: React.ComponentType<{ className?: string }>
  loading?: boolean
}) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{label}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold tabular-nums">
          {loading ? <span className="inline-block h-7 w-16 animate-pulse rounded bg-muted" /> : value}
        </div>
      </CardContent>
    </Card>
  )
}

export default DashboardMetrics


