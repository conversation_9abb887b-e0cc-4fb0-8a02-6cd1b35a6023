"use client"

import * as React from "react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { UserPlus, Search, ClipboardList, CreditCard } from "lucide-react"

export interface QuickActionsProps {
  onCreatePatient: () => void
  onSearchPatients: () => void
  onViewPendingTreatments: () => void
  onProcessPayments: () => void
  className?: string
}

export function QuickActions({
  onCreatePatient,
  onSearchPatients,
  onViewPendingTreatments,
  onProcessPayments,
  className,
}: QuickActionsProps) {
  return (
    <div className={cn("grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-4", className)}>
      <ActionButton onClick={onCreatePatient} icon={UserPlus} label="Create Patient" />
      <ActionButton onClick={onSearchPatients} icon={Search} label="Search Patients" />
      <ActionButton onClick={onViewPendingTreatments} icon={ClipboardList} label="Pending Treatments" />
      <ActionButton onClick={onProcessPayments} icon={CreditCard} label="Process Payments" />
    </div>
  )
}

function ActionButton({
  onClick,
  icon: Icon,
  label,
}: {
  onClick: () => void
  icon: React.ComponentType<{ className?: string }>
  label: string
}) {
  return (
    <Button
      type="button"
      variant="secondary"
      onClick={onClick}
      className="h-12 w-full justify-start gap-2 text-left"
    >
      <Icon className="h-4 w-4" />
      <span className="text-sm font-medium">{label}</span>
    </Button>
  )
}

export default QuickActions


