"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Home, Users, CreditCard, BarChart3 } from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { cn } from "@/lib/utils"

export interface AppSidebarProps {
  className?: string
  notificationCounts?: {
    dashboard?: number
    patients?: number
    billing?: number
    reports?: number
  }
}

export interface SidebarItem {
  title: string
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>
  href: string
  badge?: string | number
}

const sidebarItems: SidebarItem[] = [
  { title: "Dashboard", icon: Home, href: "/dashboard" },
  { title: "Patients", icon: Users, href: "/dashboard/patients" },
  { title: "Billing", icon: CreditCard, href: "/dashboard/billing" },
  { title: "Reports", icon: BarChart3, href: "/dashboard/reports" },
]

export function AppSidebar({ className, notificationCounts }: AppSidebarProps) {
  const pathname = usePathname()
  return (
    <Sidebar className={className}>
      <SidebarHeader />
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {sidebarItems.map((item) => {
                const isActive = item.href === "/dashboard"
                  ? pathname === "/dashboard"
                  : pathname.startsWith(item.href)
                const count =
                  item.title === "Dashboard"
                    ? notificationCounts?.dashboard
                    : item.title === "Patients"
                    ? notificationCounts?.patients
                    : item.title === "Billing"
                    ? notificationCounts?.billing
                    : item.title === "Reports"
                    ? notificationCounts?.reports
                    : undefined
                return (
                  <SidebarMenuItem key={item.href}>
                    <SidebarMenuButton asChild size="lg" isActive={isActive}>
                      <Link href={item.href}>
                        <item.icon className="h-5 w-5" />
                        <span className={cn("flex items-center gap-2", "md:text-sm text-base")}> 
                          {item.title}
                          {typeof count === "number" && count > 0 ? (
                            <span className="inline-flex min-w-[20px] items-center justify-center rounded-full bg-primary px-1 text-[10px] font-medium text-primary-foreground">
                              {count > 99 ? "99+" : count}
                            </span>
                          ) : null}
                        </span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  )
}
