"use client"

import * as React from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export type PatientSearchType = "phone" | "name" | "id"

export interface SearchCriteria {
  query: string
  searchType: PatientSearchType
}

export interface PatientSearchFormProps {
  onSearch: (criteria: SearchCriteria) => void
  onCreateNew: () => void
  loading?: boolean
  defaultType?: PatientSearchType
  defaultQuery?: string
  className?: string
  showSearchButton?: boolean
  showCreateButton?: boolean
  showResetButton?: boolean
  onReset?: () => void
  // When provided, this will be called on reset to reload the default table data
  onResetToDefaultList?: () => void
}

export function PatientSearchForm({
  onSearch,
  onCreateNew,
  loading,
  defaultType = "phone",
  defaultQuery = "",
  className,
  showSearchButton = true,
  showCreateButton = true,
  showResetButton = false,
  onReset,
  onResetToDefaultList,
}: PatientSearchFormProps) {
  const [type, setType] = React.useState<PatientSearchType>(defaultType)
  const [query, setQuery] = React.useState<string>(defaultQuery)

  const submit = (e?: React.FormEvent) => {
    e?.preventDefault()
    onSearch({ query: query.trim(), searchType: type })
  }

  const handleReset = () => {
    setType(defaultType)
    setQuery(defaultQuery)
    // Prefer reloading the default list if provided; otherwise fall back to legacy onReset
    if (onResetToDefaultList) {
      onResetToDefaultList()
    } else {
      onReset?.()
    }
  }

  return (
    <form onSubmit={submit} className={className}>
      <div className="flex flex-wrap items-center gap-2">
        <Select value={type} onValueChange={(v) => setType(v as PatientSearchType)}>
          <SelectTrigger className="h-9 w-[140px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="phone">By Phone</SelectItem>
            <SelectItem value="name">By Name</SelectItem>
            <SelectItem value="id">By ID</SelectItem>
          </SelectContent>
        </Select>

        <Input
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder={
            type === "phone" ? "Enter phone number" : type === "name" ? "Enter name" : "Enter patient ID"
          }
          type={type === "phone" ? "tel" : "text"}
          inputMode={type === "phone" ? "tel" : type === "id" ? "numeric" : undefined}
          autoComplete={type === "phone" ? "tel" : undefined}
          className="h-11 md:h-9 w-[280px]"
        />

        {showSearchButton && (
          <Button type="submit" disabled={loading} className="h-11 md:h-9">
            {loading ? "Searching..." : "Search"}
          </Button>
        )}

        {showCreateButton && (
          <Button type="button" variant="outline" onClick={onCreateNew} className="h-11 md:h-9">
            Create Patient
          </Button>
        )}

        {showResetButton && (
          <Button type="button" variant="outline" onClick={handleReset} className="h-11 md:h-9 ml-auto">
            Reset
          </Button>
        )}
      </div>
    </form>
  )
}
