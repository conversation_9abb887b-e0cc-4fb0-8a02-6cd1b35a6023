"use client"

import * as React from "react"
import { format } from "date-fns"
import { DataTable, type ColumnDef } from "@/components/ui/DataTable"
import { EmptyState } from "@/components/ui/EmptyState"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { ConfirmDialog } from "@/components/ui/ConfirmDialog"

export interface Patient {
  id: number | string
  firstName: string
  lastName: string
  phoneNumber: string
  lastVisitDate?: string | Date | null
  caseSheetStatus?: string | null
}

export interface PatientTableProps {
  patients: Patient[]
  className?: string
  onDeleted?: (patientId: Patient["id"]) => void
}

export function PatientTable({ patients, className, onDeleted }: PatientTableProps) {
  const router = useRouter()
  const [confirmOpen, setConfirmOpen] = React.useState(false)
  const [pendingDeleteId, setPendingDeleteId] = React.useState<Patient["id"] | null>(null)

  const handleEdit = (id: Patient["id"]) => {
    router.push(`/dashboard/patients/${id}`)
  }

  const requestDelete = (id: Patient["id"]) => {
    setPendingDeleteId(id)
    setConfirmOpen(true)
  }

  const confirmDelete = async () => {
    if (!pendingDeleteId) return
    try {
      const res = await fetch(`/api/patients/${pendingDeleteId}`, { method: "DELETE" })
      if (!res.ok) throw new Error("Failed to delete patient")
      onDeleted?.(pendingDeleteId)
      // Fallback: refresh list if parent didn't handle removal
      if (!onDeleted) {
        try { window.location.reload() } catch {}
      }
    } finally {
      setConfirmOpen(false)
      setPendingDeleteId(null)
    }
  }

  const columns = React.useMemo<ColumnDef<Patient>[]>(
    () => [
      {
        id: "name",
        header: "Name",
        cell: (row) => <Link href={`/dashboard/patients/${row.id}`} className="underline">{`${row.firstName} ${row.lastName}`}</Link>,
        sortable: true,
        sortAccessor: (row) => `${row.lastName} ${row.firstName}`.toLowerCase(),
      },
      {
        id: "phone",
        header: "Phone",
        accessor: (row) => <span className="font-mono">{row.phoneNumber}</span>,
        sortable: true,
      },
      {
        id: "lastVisit",
        header: "Last Visit",
        accessor: (row) => formatLastVisit(row.lastVisitDate),
        sortable: true,
        sortAccessor: (row) => toTime(row.lastVisitDate ?? null),
      },
      {
        id: "caseSheet",
        header: "Case Sheet",
        accessor: (row) => (row.caseSheetStatus ? normalizeStatus(row.caseSheetStatus) : "—"),
        sortable: true,
        sortAccessor: (row) => (row.caseSheetStatus ? row.caseSheetStatus.toLowerCase() : ""),
      },
      {
        id: "actions",
        header: () => <div className="flex items-center gap-2 justify-end">Actions</div>,
        cell: (row) => (
          <div className="flex items-center gap-2 justify-end">
            <Button size="sm" variant="outline" onClick={() => handleEdit(row.id)}>Edit</Button>
            <Button size="sm" variant="destructive" onClick={() => requestDelete(row.id)}>Delete</Button>
          </div>
        ),
      },
    ],
    []
  )

  const [selected, setSelected] = React.useState<React.Key[]>([])

  return (
    <>
      <DataTable
        data={patients}
        columns={columns}
        showColumnControls
        enableBulkSelection
        selectedRowKeys={selected}
        onSelectedRowKeysChange={setSelected}
        bulkActions={
          selected.length > 0 ? (
            <div className="text-sm text-muted-foreground">{selected.length} selected</div>
          ) : null
        }
        className={className}
        emptyState={<EmptyState title="No patients found" description="Adjust your search or create a new patient." />}
      />

      <ConfirmDialog
        open={confirmOpen}
        onOpenChange={setConfirmOpen}
        title="Delete patient?"
        description="This action cannot be undone and will remove the patient record."
        confirmText="Delete"
        destructive
        onConfirm={confirmDelete}
        onCancel={() => { setConfirmOpen(false); setPendingDeleteId(null) }}
      />
    </>
  )
}

function formatLastVisit(value?: string | Date | null): string {
  if (!value) return "—"
  const date = value instanceof Date ? value : new Date(value)
  if (isNaN(date.getTime())) return "—"
  try {
    return format(date, "LLL dd, y")
  } catch {
    return "—"
  }
}

function toTime(value?: string | Date | null): number {
  if (!value) return 0
  const date = value instanceof Date ? value : new Date(value)
  return isNaN(date.getTime()) ? 0 : date.getTime()
}

function normalizeStatus(status: string): string {
  const s = status.toLowerCase()
  if (s === "none") return "None"
  if (s === "active") return "Active"
  if (s === "completed") return "Completed"
  return status
}
