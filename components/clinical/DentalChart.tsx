"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { ToothComponent, type ToothStatus } from "./ToothComponent"
import { DentalChartLegend } from "./DentalChartLegend"
import { usePinchZoom } from "@/hooks/usePinchZoom"

export interface DentalChartProps {
  className?: string
  toothSize?: number
  statusByTooth?: Partial<Record<number, ToothStatus>>
  selectedTooth?: number
  onToothClick?: (toothNumber: number) => void
  onSelectionChange?: (toothNumber: number) => void
  tooltipDataByTooth?: Partial<Record<number, { findings?: Array<{ id?: number | string; description?: string; severity?: "low" | "medium" | "high"; createdAt?: string | Date }>; treatments?: Array<{ id?: number | string; procedureName?: string; status?: "pending" | "in-progress" | "completed"; cost?: number }> }>>
}

// FDI quadrants
const Q1_UPPER_RIGHT = [11, 12, 13, 14, 15, 16, 17, 18]
const Q2_UPPER_LEFT = [21, 22, 23, 24, 25, 26, 27, 28]
const Q3_LOWER_LEFT = [31, 32, 33, 34, 35, 36, 37, 38]
const Q4_LOWER_RIGHT = [41, 42, 43, 44, 45, 46, 47, 48]

function getQuadrantName(number: number): string {
  const quadrant = Math.floor(number / 10)
  return quadrant === 1
    ? "Upper Right"
    : quadrant === 2
    ? "Upper Left"
    : quadrant === 3
    ? "Lower Left"
    : "Lower Right"
}

function QuadrantRow({
  numbers,
  size,
  statusByTooth,
  selectedTooth,
  onToothClick,
  reverse,
  tooltipDataByTooth,
}: {
  numbers: number[]
  size: number
  statusByTooth?: Partial<Record<number, ToothStatus>>
  selectedTooth?: number
  onToothClick?: (toothNumber: number) => void
  reverse?: boolean
  tooltipDataByTooth?: DentalChartProps["tooltipDataByTooth"]
}) {
  const title = getQuadrantName(numbers[0])
  const sequence = reverse ? [...numbers].reverse() : numbers
  return (
    <div className="flex flex-col items-center gap-2">
      <div className="text-xs font-medium text-muted-foreground">{title}</div>
      <div className={cn("flex gap-2")}> 
        {sequence.map((n) => {
          const status = statusByTooth?.[n]
          const isSelected = selectedTooth === n
          const tooltipData = tooltipDataByTooth?.[n]
          return (
            <ToothComponent
              key={n}
              number={n}
              size={size}
              status={status}
              selected={isSelected}
              onClick={onToothClick ? () => onToothClick(n) : undefined}
              tooltipFindings={tooltipData?.findings}
              tooltipTreatments={tooltipData?.treatments}
            />
          )
        })}
      </div>
    </div>
  )
}

export function DentalChart({
  className,
  toothSize = 40,
  statusByTooth,
  selectedTooth,
  onToothClick,
  onSelectionChange,
  tooltipDataByTooth,
}: DentalChartProps) {
  // Responsive sizing: compute tooth size from available width and breakpoint
  const containerRef = React.useRef<HTMLDivElement | null>(null)
  const [isMdUp, setIsMdUp] = React.useState<boolean>(
    typeof window !== "undefined" ? window.matchMedia("(min-width: 768px)").matches : false
  )
  const [computedToothSize, setComputedToothSize] = React.useState<number>(toothSize)

  React.useEffect(() => {
    if (typeof window === "undefined") return
    const mql = window.matchMedia("(min-width: 768px)")
    const handler = (e: MediaQueryListEvent) => setIsMdUp(e.matches)
    // Initial sync (for SSR hydration safety)
    setIsMdUp(mql.matches)
    mql.addEventListener("change", handler)
    return () => mql.removeEventListener("change", handler)
  }, [])

  const recalcToothSize = React.useCallback(() => {
    const gridEl = containerRef.current
    if (!gridEl) return
    const gridWidth = gridEl.clientWidth
    if (gridWidth <= 0) return

    const numColumns = isMdUp ? 2 : 1
    // Tailwind: gap-8 (32px) on base, gap-12 (48px) on md+
    const gridGapPx = isMdUp ? 48 : 32
    const columnWidth =
      numColumns === 1 ? gridWidth : Math.max(0, (gridWidth - gridGapPx) / numColumns)

    // Each quadrant row has 8 teeth, with gap-2 (8px) between items
    const teethPerRow = 8
    const toothGapPx = 8
    // Card adds extra horizontal chrome beyond "size": +18px in ToothComponent
    const toothCardExtraPx = 18
    const maxSizeByWidth = Math.floor(
      (columnWidth - (teethPerRow - 1) * toothGapPx) / teethPerRow - toothCardExtraPx
    )

    const minSize = 24
    const capSize = toothSize ?? 56
    const nextSize = Math.max(minSize, Math.min(maxSizeByWidth, capSize))
    if (Number.isFinite(nextSize) && nextSize > 0) {
      setComputedToothSize(nextSize)
    }
  }, [isMdUp, toothSize])

  React.useLayoutEffect(() => {
    recalcToothSize()
  }, [recalcToothSize])

  React.useEffect(() => {
    const gridEl = containerRef.current
    if (!gridEl) return
    const observer = new ResizeObserver(() => recalcToothSize())
    observer.observe(gridEl)
    window.addEventListener("resize", recalcToothSize)
    return () => {
      observer.disconnect()
      window.removeEventListener("resize", recalcToothSize)
    }
  }, [recalcToothSize])
  const [internalSelectedTooth, setInternalSelectedTooth] = React.useState<number | undefined>(
    undefined
  )

  const effectiveSelectedTooth =
    typeof selectedTooth === "number" ? selectedTooth : internalSelectedTooth

  const handleToothClick = React.useCallback(
    (toothNumber: number) => {
      if (typeof selectedTooth !== "number") {
        setInternalSelectedTooth(toothNumber)
      }
      onSelectionChange?.(toothNumber)
      onToothClick?.(toothNumber)
    },
    [onToothClick, onSelectionChange, selectedTooth]
  )

  const pinch = usePinchZoom({ minScale: 1, maxScale: 3 })

  return (
    <div className={cn("w-full space-y-6", className)}>
      <DentalChartLegend />
      <div ref={pinch.ref} style={pinch.style} {...pinch.handlers} className="relative select-none px-2 md:px-6">
        {/* Cross divider */}
        <div className="pointer-events-none absolute inset-0 hidden md:block">
          <div className="absolute left-1/2 top-4 -ml-px h-[calc(100%-2rem)] w-px bg-border/70" />
          <div className="absolute left-4 top-1/2 -mt-px h-px w-[calc(100%-2rem)] bg-border/70" />
        </div>

        {/* 2x2 quadrant grid */}
        <div ref={containerRef} className="w-full">
          <div className="grid grid-cols-1 gap-4 xs:gap-6 sm:gap-8 md:grid-cols-2 md:gap-12 place-items-center">
          {/* Top-left: Upper Left (Q2) */}
          <QuadrantRow
            numbers={Q2_UPPER_LEFT}
            size={computedToothSize}
            statusByTooth={statusByTooth}
            selectedTooth={effectiveSelectedTooth}
            onToothClick={handleToothClick}
            tooltipDataByTooth={tooltipDataByTooth}
            reverse
          />
          {/* Top-right: Upper Right (Q1) */}
          <QuadrantRow
            numbers={Q1_UPPER_RIGHT}
            size={computedToothSize}
            statusByTooth={statusByTooth}
            selectedTooth={effectiveSelectedTooth}
            onToothClick={handleToothClick}
            tooltipDataByTooth={tooltipDataByTooth}
          />
          {/* Bottom-left: Lower Left (Q3) */}
          <QuadrantRow
            numbers={Q3_LOWER_LEFT}
            size={computedToothSize}
            statusByTooth={statusByTooth}
            selectedTooth={effectiveSelectedTooth}
            onToothClick={handleToothClick}
            tooltipDataByTooth={tooltipDataByTooth}
            reverse
          />
          {/* Bottom-right: Lower Right (Q4) */}
          <QuadrantRow
            numbers={Q4_LOWER_RIGHT}
            size={computedToothSize}
            statusByTooth={statusByTooth}
            selectedTooth={effectiveSelectedTooth}
            onToothClick={handleToothClick}
            tooltipDataByTooth={tooltipDataByTooth}
          />
          </div>
        </div>
      </div>
    </div>
  )
}
