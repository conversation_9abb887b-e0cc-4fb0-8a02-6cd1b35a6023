"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip"
import { ToothTooltip } from "./ToothTooltip"

export interface ToothComponentProps {
  number: number
  size?: number
  className?: string
  onClick?: () => void
  status?: ToothStatus
  selected?: boolean
  tooltipFindings?: Array<{
    id?: number | string
    description?: string
    severity?: "low" | "medium" | "high"
    createdAt?: string | Date
  }>
  tooltipTreatments?: Array<{
    id?: number | string
    procedureName?: string
    status?: "pending" | "in-progress" | "completed"
    cost?: number
  }>
}

export interface ToothStatus {
  present: boolean
  hasFindings: boolean
  hasPendingTreatments: boolean
  hasCompletedTreatments: boolean
}

function getToothTypeName(number: number): string {
  const typeDigit = number % 10
  const typeNameMap: Record<number, string> = {
    1: "Central Incisor",
    2: "Lateral Incisor",
    3: "Canine",
    4: "First Premolar",
    5: "Second Premolar",
    6: "First Molar",
    7: "Second Molar",
    8: "Third Molar",
  }
  return typeNameMap[typeDigit] ?? "Tooth"
}

type StatusVariant = "none" | "missing" | "finding" | "pending" | "completed"

function getStatusVariant(status?: ToothStatus): StatusVariant {
  if (!status) return "none"
  if (status.present === false) return "missing"
  if (status.hasFindings) return "finding"
  if (status.hasPendingTreatments) return "pending"
  if (status.hasCompletedTreatments) return "completed"
  return "none"
}

export const ToothComponent = React.memo(function ToothComponent({
  number,
  size = 40,
  className,
  onClick,
  status,
  selected = false,
  tooltipFindings = [],
  tooltipTreatments = [],
}: ToothComponentProps) {
  const src = `/teeth/${number}.svg`
  const typeName = getToothTypeName(number)
  const variant = getStatusVariant(status)

  const ringClass =
    variant === "finding"
      ? "ring-2 ring-red-500"
      : variant === "pending"
      ? "ring-2 ring-amber-500"
      : variant === "completed"
      ? "ring-2 ring-emerald-500"
      : "ring-1 ring-border"

  const selectedRingClass = selected ? "ring-offset-2 ring-offset-background" : ""

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <button
          type="button"
          onClick={onClick}
          className={cn(
            "group relative flex flex-col items-center gap-1 select-none focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background",
            className
          )}
          aria-label={`${typeName} (Tooth ${number})`}
          title={typeName}
        >
      {/* Visual representation */}
      <div
        className={cn(
          "relative rounded-md flex items-center justify-center overflow-hidden bg-background",
          ringClass,
          selectedRingClass
        )}
        style={{ width: size + 18, height: size + 42, padding: 6 }}
      >
        {/* Images are expected at public/teeth/<FDI>.svg */}
        <img
          src={src}
          alt={typeName}
          className={cn(
            "block w-full h-full object-contain rounded-sm"
          )}
        />

        {/* Status badges (center-top). If missing, we don't render these. */}
        {status?.present !== false && (status?.hasFindings || status?.hasPendingTreatments || status?.hasCompletedTreatments) && (
          <div className="absolute top-1 left-1/2 -translate-x-1/2 z-20 flex items-center gap-1">
            {status?.hasFindings && (
              <span className="h-2.5 w-2.5 rounded-full border border-background bg-red-500" aria-hidden />
            )}
            {status?.hasPendingTreatments && (
              <span className="h-2.5 w-2.5 rounded-full border border-background bg-amber-500" aria-hidden />
            )}
            {status?.hasCompletedTreatments && (
              <span className="h-2.5 w-2.5 rounded-full border border-background bg-emerald-500" aria-hidden />
            )}
          </div>
        )}

        {/* No explicit missing badge rendering */}
      </div>

      {/* Labels */}
      <div className="text-center leading-tight mt-1">
        <div className="text-[10px] text-muted-foreground">{number}</div>
        <div
          className={cn(
            "max-w-24 text-[10px] text-muted-foreground"
          )}
        >
          {typeName}
        </div>
      </div>
        </button>
      </TooltipTrigger>
      <TooltipContent sideOffset={8}>
        <ToothTooltip
          toothNumber={number}
          toothName={typeName}
          findings={tooltipFindings}
          treatments={tooltipTreatments}
        />
      </TooltipContent>
    </Tooltip>
  )
})

export default ToothComponent
