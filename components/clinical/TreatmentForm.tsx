"use client"

import * as React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { cn } from "@/lib/utils"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { treatmentFormSchema, type TreatmentFormData } from "@/lib/types/treatment-schemas"
import { Alert, AlertDescription } from "@/components/ui/alert"
import Combobox from "@/components/ui/Combobox"
import { TREATMENT_GROUPS } from "@/lib/clinical/taxonomy"

export interface TreatmentFormProps {
  defaultValues?: Partial<TreatmentFormData>
  loading?: boolean
  onSubmit: (data: TreatmentFormData) => Promise<void> | void
  onCancel?: () => void
  className?: string
}

export function TreatmentForm({ defaultValues, loading = false, onSubmit, onCancel, className }: TreatmentFormProps) {
  const form = useForm<TreatmentFormData>({
    resolver: zodResolver(treatmentFormSchema),
    defaultValues: {
      procedureName: "",
      cost: defaultValues?.cost ?? "",
      notes: "",
      ...defaultValues,
    },
    mode: "onChange",
  })

  const handleSubmit = form.handleSubmit(async (values) => {
    try {
      await onSubmit(values)
    } catch (err) {
      const message = err instanceof Error ? err.message : "Failed to save treatment. Please try again."
      form.setError("root", { type: "server", message })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className={cn("flex flex-col gap-4", className)}>
        {form.formState.errors.root?.message ? (
          <Alert variant="destructive">
            <AlertDescription>{form.formState.errors.root.message}</AlertDescription>
          </Alert>
        ) : null}
        <FormField
          control={form.control}
          name="procedureName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Procedure name</FormLabel>
              <FormControl>
                <Combobox
                  value={field.value ?? ""}
                  onChange={field.onChange}
                  groups={TREATMENT_GROUPS}
                  placeholder="Search or type procedure"
                  emptyMessage="No results. Press Enter to use input."
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="cost"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Cost</FormLabel>
              <FormControl>
                <Input
                  type="text"
                  inputMode="decimal"
                  placeholder="0.00"
                  enterKeyHint="done"
                  className="h-11 md:h-9"
                  {...field}
                />
              </FormControl>
              <FormDescription>Enter the treatment cost in your clinic's currency.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes (optional)</FormLabel>
              <FormControl>
                <Textarea rows={3} placeholder="Additional details..." className="min-h-[44px]" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="mt-2 flex items-center justify-end gap-2">
          {onCancel && (
            <Button type="button" variant="ghost" onClick={onCancel} className="min-w-[80px]">
              Cancel
            </Button>
          )}
          <Button type="submit" className="min-w-[140px]" disabled={loading}>
            {loading ? "Saving..." : "Save Treatment"}
          </Button>
        </div>
      </form>
    </Form>
  )
}

export default TreatmentForm


