"use client"

import * as React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { cn } from "@/lib/utils"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import Combobox from "@/components/ui/Combobox"
import { FINDING_GROUPS } from "@/lib/clinical/taxonomy"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { findingFormSchema, type FindingFormData } from "@/lib/types/finding-schemas"

export interface FindingFormProps {
  defaultValues?: Partial<FindingFormData>
  loading?: boolean
  onSubmit: (data: FindingFormData) => Promise<void> | void
  onCancel?: () => void
  className?: string
  onCreateTreatment?: () => void
  showCreateTreatmentPrompt?: boolean
}

export function FindingForm({
  defaultValues,
  loading = false,
  onSubmit,
  onCancel,
  className,
  onCreateTreatment,
  showCreateTreatmentPrompt = true,
}: FindingFormProps) {
  const form = useForm<FindingFormData>({
    resolver: zodResolver(findingFormSchema),
    defaultValues: {
      description: "",
      severity: undefined,
      ...defaultValues,
    },
    mode: "onChange",
  })

  const [isSaved, setIsSaved] = React.useState(false)

  const handleSubmit = form.handleSubmit(async (values) => {
    try {
      await onSubmit(values)
      setIsSaved(true)
    } catch (err) {
      const message = err instanceof Error ? err.message : "Failed to save finding. Please try again."
      form.setError("root", { type: "server", message })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className={cn("flex flex-col gap-4", className)}>
        {form.formState.errors.root?.message ? (
          <Alert variant="destructive">
            <AlertDescription>{form.formState.errors.root.message}</AlertDescription>
          </Alert>
        ) : null}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Combobox
                  value={field.value ?? ""}
                  onChange={field.onChange}
                  groups={FINDING_GROUPS}
                  placeholder="Search or type description..."
                  emptyMessage="No results. Press Enter to use input."
                />
              </FormControl>
              <FormDescription>Select from the list or enter a custom description.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="severity"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Severity (optional)</FormLabel>
              <FormControl>
                <Select
                  value={field.value ?? ""}
                  onValueChange={(v) => field.onChange(v as FindingFormData["severity"])}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select severity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="mt-2 flex items-center justify-end gap-2">
          {onCancel && (
            <Button type="button" variant="ghost" onClick={onCancel} className="min-w-[80px]">
              Cancel
            </Button>
          )}
          <Button type="submit" className="min-w-[120px]" disabled={loading}>
            {loading ? "Saving..." : "Save Finding"}
          </Button>
        </div>
      </form>
      {isSaved && onCreateTreatment && showCreateTreatmentPrompt && (
        <Alert className="mt-3">
          <AlertTitle>Finding saved</AlertTitle>
          <AlertDescription className="mt-1 flex items-center justify-between gap-2">
            <span>Would you like to create a treatment for this finding now?</span>
            <Button size="sm" onClick={onCreateTreatment} className="ml-2 min-w-[140px]">
              Create Treatment
            </Button>
          </AlertDescription>
        </Alert>
      )}
    </Form>
  )
}

export default FindingForm


