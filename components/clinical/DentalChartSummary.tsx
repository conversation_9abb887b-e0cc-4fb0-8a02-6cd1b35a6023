"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { Separator } from "@/components/ui/separator"
import { formatCurrency } from "@/lib/currency"
import { Skeleton } from "@/components/ui/skeleton"
import { ClipboardList, AlertTriangle, CheckCircle2, Clock3 } from "lucide-react"
import { calculateQuadrant, calculatePosition } from "@/lib/clinical/fdi-numbering"

export type TreatmentStatus = "pending" | "in-progress" | "completed"

export interface SummaryTreatment {
  id: number | string
  procedureName: string
  status: TreatmentStatus
  cost?: number
  notes?: string
}

export interface SummaryFinding {
  id: number | string
  description: string
  severity?: "low" | "medium" | "high"
  createdAt?: string | Date
  treatments: SummaryTreatment[]
}

export interface SummaryTooth {
  toothNumber: number
  status?: string
  findings: SummaryFinding[]
}

export interface DentalChartSummaryProps {
  teeth: SummaryTooth[]
  loading?: boolean
  className?: string
}

export function DentalChartSummary({ teeth, loading = false, className }: DentalChartSummaryProps) {
  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Clinical Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="flex items-start gap-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-2/3" />
                  <Skeleton className="h-3 w-1/3" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  const sortedTeeth = [...(teeth ?? [])]
    .filter((t) => (t.findings ?? []).length > 0)
    .sort((a, b) => a.toothNumber - b.toothNumber)

  const totals = React.useMemo(() => {
    const findings = sortedTeeth.reduce((acc, t) => acc + (t.findings?.length ?? 0), 0)
    const treatments = sortedTeeth.reduce(
      (acc, t) => acc + t.findings.reduce((s, f) => s + (f.treatments?.length ?? 0), 0),
      0
    )
    const grandTotal = sortedTeeth.reduce((sum, t) => {
      return (
        sum +
        t.findings.reduce((fs, f) => fs + f.treatments.reduce((ts, tr) => ts + Number(tr.cost ?? 0), 0), 0)
      )
    }, 0)
    return { findings, treatments, grandTotal }
  }, [sortedTeeth])

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Clinical Summary</CardTitle>
          <div className="text-xs text-muted-foreground">
            {totals.findings} findings • {totals.treatments} treatments • {formatCurrency(totals.grandTotal)}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {sortedTeeth.length === 0 ? (
          <div className="text-sm text-muted-foreground">No findings or treatments yet.</div>
        ) : (
          <ul className="space-y-4">
            {sortedTeeth.map((tooth) => (
              <li key={tooth.toothNumber} className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="rounded-full bg-primary/10 p-1.5">
                    <ClipboardList className="h-5 w-5 text-primary" />
                  </div>
                  <div className="min-w-0">
                    <div className="text-sm font-medium">
                      Tooth {tooth.toothNumber}
                      {(() => {
                        try {
                          const q = calculateQuadrant(Number(tooth.toothNumber))
                          const p = calculatePosition(Number(tooth.toothNumber))
                          return (
                            <span className="ml-2 text-muted-foreground font-normal">
                              • {QUADRANT_NAME[q]} • {POSITION_NAME[p]}
                            </span>
                          )
                        } catch {
                          return null
                        }
                      })()}
                    </div>
                    <div className="text-xs text-muted-foreground">{tooth.status ? prettifyStatus(tooth.status) : ""}</div>
                  </div>
                </div>

                <ul className="ml-10 space-y-3">
                  {tooth.findings.map((f) => (
                    <li key={f.id} className="space-y-2">
                      <div className="flex items-start gap-3">
                        <div className="rounded-full bg-amber-500/10 p-1.5">
                          <AlertTriangle className="h-4 w-4 text-amber-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className="flex items-center gap-2">
                            <p className="text-sm">{f.description}</p>
                            {f.severity ? <SeverityBadge level={f.severity} /> : null}
                          </div>
                          <div className="mt-1 text-[11px] text-muted-foreground">
                            {f.createdAt ? formatDate(f.createdAt) : null}
                          </div>
                        </div>
                      </div>

                      <ul className="ml-8 space-y-2">
                        {f.treatments.length === 0 ? (
                          <li className="text-xs text-muted-foreground">No treatments planned.</li>
                        ) : (
                          f.treatments.map((t) => (
                            <li key={t.id} className="flex items-start gap-3">
                              <div className={cn(
                                "rounded-full p-1.5",
                                t.status === "completed"
                                  ? "bg-emerald-500/10"
                                  : t.status === "in-progress"
                                  ? "bg-sky-500/10"
                                  : "bg-amber-500/10"
                              )}>
                                {t.status === "completed" ? (
                                  <CheckCircle2 className="h-4 w-4 text-emerald-600" />
                                ) : (
                                  <Clock3 className={cn("h-4 w-4", t.status === "in-progress" ? "text-sky-600" : "text-amber-600")} />
                                )}
                              </div>
                              <div className="min-w-0 flex-1">
                                <div className="flex items-center gap-2">
                                  <p className="truncate text-sm font-medium">{t.procedureName}</p>
                                  <StatusBadge status={t.status} />
                                </div>
                                <div className="mt-1 text-[11px] text-muted-foreground">
                                  {Number.isFinite(Number(t.cost)) ? (
                                    <span>Cost: {formatCurrency(Number(t.cost ?? 0))}</span>
                                  ) : null}
                                  {t.notes ? <span className="ml-2">• {t.notes}</span> : null}
                                </div>
                              </div>
                            </li>
                          ))
                        )}
                      </ul>
                    </li>
                  ))}
                </ul>

                <Separator />
              </li>
            ))}
          </ul>
        )}
      </CardContent>
    </Card>
  )
}

function StatusBadge({ status }: { status: TreatmentStatus }) {
  const color =
    status === "completed"
      ? "bg-emerald-600 text-white"
      : status === "in-progress"
      ? "bg-sky-600 text-white"
      : "bg-amber-600 text-white"
  const label = status === "completed" ? "Completed" : status === "in-progress" ? "In progress" : "Pending"
  return <span className={cn("inline-flex items-center rounded-full px-2 py-0.5 text-[10px] font-medium", color)}>{label}</span>
}

function SeverityBadge({ level }: { level: "low" | "medium" | "high" }) {
  const style =
    level === "high"
      ? "bg-red-500/15 text-red-600 border-red-500/30"
      : level === "medium"
      ? "bg-amber-500/15 text-amber-700 border-amber-500/30"
      : "bg-emerald-500/15 text-emerald-700 border-emerald-500/30"
  const label = level === "high" ? "High" : level === "medium" ? "Medium" : "Low"
  return <span className={cn("inline-flex items-center rounded-full border px-2 py-0.5 text-[10px] font-medium", style)}>{label}</span>
}

const QUADRANT_NAME: Record<1 | 2 | 3 | 4, string> = {
  1: "Upper Right",
  2: "Upper Left",
  3: "Lower Left",
  4: "Lower Right",
}

const POSITION_NAME: Record<number, string> = {
  1: "Central Incisor",
  2: "Lateral Incisor",
  3: "Canine",
  4: "First Premolar",
  5: "Second Premolar",
  6: "First Molar",
  7: "Second Molar",
  8: "Third Molar",
}

function prettifyStatus(status?: string) {
  if (!status) return ""
  try {
    const s = String(status)
    if (s === s.toUpperCase()) return s.charAt(0) + s.slice(1).toLowerCase()
    return s
  } catch {
    return String(status)
  }
}

function formatDate(value: string | Date): string {
  try {
    const date = typeof value === "string" ? new Date(value) : value
    return new Intl.DateTimeFormat(undefined, {
      year: "numeric",
      month: "short",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  } catch {
    return String(value)
  }
}

export default DentalChartSummary


