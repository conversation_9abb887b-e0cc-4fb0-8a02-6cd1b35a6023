"use client"

import * as React from "react"
import { toast } from "sonner"
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import InvoiceStatusBadge, { type InvoiceStatus as SharedInvoiceStatus } from "@/components/billing/InvoiceStatusBadge"
import { formatCurrency } from "@/lib/currency"

export type InvoiceStatus = SharedInvoiceStatus

export interface InvoiceTreatmentItem {
  id: number | string
  procedureName: string
  status: "pending" | "in-progress" | "completed"
  cost: number
  notes?: string
}

export interface InvoicePaymentItem {
  id: number | string
  amount: number
  date: string | Date
  method?: string
}

export interface InvoiceDetailsData {
  id: number | string
  serial: string
  date: string | Date
  status: InvoiceStatus
  patient: { id: number | string; name: string }
  treatments: InvoiceTreatmentItem[]
  payments?: InvoicePaymentItem[]
  totalAmount: number
  amountPaid: number
  balanceDue: number
}

export interface InvoiceDetailsProps {
  invoice: InvoiceDetailsData
  className?: string
  onEdit?: () => void
  onProcessPayment?: () => void
  onSendInvoice?: () => void
  onPaymentSuccess?: (invoiceId: InvoiceDetailsData["id"]) => void
  onAutoMarkPaid?: (invoiceId: InvoiceDetailsData["id"]) => void
  onCancelInvoice?: () => void
}

export function InvoiceDetails({ invoice, className, onEdit, onProcessPayment, onSendInvoice, onPaymentSuccess, onAutoMarkPaid, onCancelInvoice }: InvoiceDetailsProps) {
  const printRef = React.useRef<HTMLDivElement>(null)
  const subtotal = invoice.treatments.reduce((sum, t) => sum + (t.cost ?? 0), 0)
  const total = formatCurrency(invoice.totalAmount ?? subtotal)
  const paid = formatCurrency(invoice.amountPaid ?? 0)
  const balance = formatCurrency(invoice.balanceDue ?? Math.max(0, (invoice.totalAmount ?? subtotal) - (invoice.amountPaid ?? 0)))

  const hasMarkedRef = React.useRef(false)
  React.useEffect(() => {
    if (!hasMarkedRef.current && (invoice.balanceDue ?? 0) <= 0 && invoice.status !== "paid") {
      hasMarkedRef.current = true
      onAutoMarkPaid?.(invoice.id)
      toast.success("Invoice marked as PAID")
    }
  }, [invoice.balanceDue, invoice.id, invoice.status, onAutoMarkPaid])

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="border-b">
        <div className="flex flex-wrap items-start justify-between gap-3">
          <div>
            <CardTitle>Invoice <span className="font-mono text-blue-700">{invoice.serial}</span></CardTitle>
            <CardDescription>
              {formatDate(invoice.date)} • {invoice.patient.name}
            </CardDescription>
          </div>
          <InvoiceStatusBadge status={invoice.status} />
        </div>
      </CardHeader>

      <CardContent className="py-5">
        <div ref={printRef} className="print:bg-white print:text-black">
        <div className="mb-4 text-sm font-medium">Itemized Treatments</div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Procedure</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Cost</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {invoice.treatments.map((t) => (
              <TableRow key={t.id}>
                <TableCell>
                  <div className="flex min-w-0 flex-col">
                    <span className="truncate text-sm font-medium">{t.procedureName}</span>
                    {t.notes ? (
                      <span className="text-muted-foreground truncate text-xs">{t.notes}</span>
                    ) : null}
                  </div>
                </TableCell>
                <TableCell className="text-sm capitalize">{t.status.replace("-", " ")}</TableCell>
                <TableCell className="text-right text-sm font-mono ">{formatCurrency(t.cost)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        <div className="mt-4 flex flex-col items-end gap-1 text-sm">
          <div className="flex w-full max-w-xs items-center justify-between">
            <span className="text-muted-foreground">Subtotal</span>
            <span className="font-medium font-mono text-blue-700">{formatCurrency(subtotal)}</span>
          </div>
          <div className="flex w-full max-w-xs items-center justify-between">
            <span className="text-muted-foreground">Total</span>
            <span className="font-medium font-mono text-blue-700">{total}</span>
          </div>
          <div className="flex w-full max-w-xs items-center justify-between">
            <span className="text-muted-foreground">Paid</span>
            <span className="font-medium font-mono text-green-700">{paid}</span>
          </div>
          <div className="flex w-full max-w-xs items-center justify-between">
            <span className="text-muted-foreground">Balance</span>
            <span className="text-base font-semibold font-mono text-red-700">{balance}</span>
          </div>
        </div>

        {invoice.payments && invoice.payments.length > 0 ? (
          <div className="mt-6">
            <div className="mb-2 text-sm font-medium">Payments</div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Method</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoice.payments.map((p) => (
                  <TableRow key={p.id}>
                    <TableCell className="text-sm">{formatDate(p.date)}</TableCell>
                    <TableCell className="text-sm">{p.method ?? "—"}</TableCell>
                    <TableCell className="text-right text-sm font-mono text-green-700">{formatCurrency(p.amount)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : null}
        </div>
      </CardContent>

      <CardFooter className="justify-end border-t">
        <div className="flex flex-wrap items-center gap-2">
          <Button variant="destructive" onClick={onCancelInvoice} disabled={!onCancelInvoice}>
            Cancel Invoice
          </Button>
          {invoice.status === "draft" && (
            <Button variant="secondary" onClick={onSendInvoice} disabled={!onSendInvoice}>
              Send Invoice
            </Button>
          )}
          {invoice.balanceDue > 0 && (
            <Button onClick={() => { onProcessPayment?.(); onPaymentSuccess?.(invoice.id) }} disabled={!onProcessPayment}>
              Process Payment
            </Button>
          )}
          <Button variant="outline" asChild>
            <a href={`/print/invoice/${invoice.id}?auto=1`} target="_blank" rel="noreferrer noopener">Print</a>
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}

// moved to shared component

function formatDate(value: string | Date): string {
  try {
    const d = typeof value === "string" ? new Date(value) : value
    return new Intl.DateTimeFormat(undefined, { year: "numeric", month: "short", day: "2-digit" }).format(d)
  } catch {
    return String(value)
  }
}

// currency handled by shared helper

export default InvoiceDetails


