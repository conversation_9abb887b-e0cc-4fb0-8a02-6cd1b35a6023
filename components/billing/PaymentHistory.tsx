"use client"

import * as React from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { formatCurrency } from "@/lib/currency"

export interface PaymentItem {
  id: number | string
  amount: number
  date: string | Date
  method?: string
  balanceAfter?: number
}

export interface PaymentHistoryProps {
  payments: PaymentItem[]
  invoiceTotal?: number
  className?: string
  onRefund?: (paymentId: PaymentItem["id"]) => Promise<void> | void
  onEdit?: (payment: PaymentItem) => void
  onDelete?: (paymentId: PaymentItem["id"]) => void
}

export function PaymentHistory({ payments, invoiceTotal, className, onRefund, onEdit, onDelete }: PaymentHistoryProps) {
  const rows = React.useMemo(() => {
    const sorted = [...payments].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    if (typeof invoiceTotal === "number") {
      let running = invoiceTotal
      return sorted.map((p) => {
        running = Math.max(0, running - (p.amount ?? 0))
        return { ...p, balanceAfter: p.balanceAfter ?? running }
      })
    }
    return sorted
  }, [payments, invoiceTotal])

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle>Payment History</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Method</TableHead>
              <TableHead className="text-right">Amount</TableHead>
              <TableHead className="text-right">Balance</TableHead>
              {(onEdit || onDelete || onRefund) ? <TableHead className="w-0 text-right">Actions</TableHead> : null}
            </TableRow>
          </TableHeader>
          <TableBody>
            {rows.map((p) => (
              <TableRow key={p.id}>
                <TableCell className="text-sm">{formatDate(p.date)}</TableCell>
                <TableCell className="text-sm">{p.method ?? "—"}</TableCell>
                <TableCell className="text-right text-sm font-mono text-green-700">{formatCurrency(p.amount)}</TableCell>
                <TableCell className="text-right text-sm font-mono text-red-700">{typeof p.balanceAfter === "number" ? formatCurrency(p.balanceAfter) : "—"}</TableCell>
                {(onEdit || onDelete || onRefund) ? (
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      {onEdit ? (
                        <Button variant="outline" size="sm" onClick={() => onEdit(p)}>
                          Edit
                        </Button>
                      ) : null}
                      {onDelete ? (
                        <Button variant="destructive" size="sm" onClick={() => onDelete(p.id)}>
                          Delete
                        </Button>
                      ) : null}
                      {onRefund ? (
                        <Button variant="outline" size="sm" onClick={() => onRefund(p.id)}>
                          Refund
                        </Button>
                      ) : null}
                    </div>
                  </TableCell>
                ) : null}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

function formatDate(value: string | Date): string {
  try {
    const d = typeof value === "string" ? new Date(value) : value
    return new Intl.DateTimeFormat(undefined, { year: "numeric", month: "short", day: "2-digit" }).format(d)
  } catch {
    return String(value)
  }
}

// currency handled by shared helper

export default PaymentHistory


