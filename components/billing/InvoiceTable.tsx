"use client";

import * as React from "react";
import { DataTable, type ColumnDef } from "@/components/ui/DataTable";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ConfirmDialog } from "@/components/ui/ConfirmDialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import InvoiceStatusBadge from "@/components/billing/InvoiceStatusBadge";
import { formatCurrency } from "@/lib/currency";
import Link from "next/link";

export type InvoiceStatus = "draft" | "sent" | "partially_paid" | "paid" | "overdue";

export interface InvoiceRow {
  id: number | string;
  serial: string;
  date: string | Date;
  patientName: string;
  status: InvoiceStatus;
  totalAmount: number;
  amountPaid: number;
  balanceDue: number;
}

export interface InvoiceTableFilters {
  patient?: string;
  status?: InvoiceStatus | "all";
  dateFrom?: Date;
  dateTo?: Date;
  amountMin?: number;
  amountMax?: number;
}

export interface InvoiceTableProps {
  invoices: InvoiceRow[];
  filters: InvoiceTableFilters;
  onFiltersChange: (filters: InvoiceTableFilters) => void;
  onRowClick?: (invoice: InvoiceRow) => void;
  onSendInvoice?: (invoice: InvoiceRow) => void;
  onEditInvoice?: (invoice: InvoiceRow) => void;
  onDeleteInvoice?: (invoice: InvoiceRow) => void;
}

export function InvoiceTable({
  invoices,
  filters,
  onFiltersChange,
  onRowClick,
  onSendInvoice,
  onEditInvoice,
  onDeleteInvoice,
}: InvoiceTableProps) {
  const columns: ColumnDef<InvoiceRow>[] = [
    {
      id: "number",
      header: "Invoice #",
      accessor: (r) => <Link href={`/dashboard/billing/invoices/${r.id}`} className="underline font-mono">{r.serial}</Link>,
      sortable: true,
      sortAccessor: (r) => parseNumber(r.serial),
    },
    {
      id: "date",
      header: "Date",
      accessor: (r) => <span className="font-mono">{formatDate(r.date)}</span>,
      sortable: true,
      sortAccessor: (r) => new Date(r.date).getTime(),
    },
    {
      id: "patient",
      header: "Patient",
      accessor: (r) => r.patientName,
      sortable: true,
    },
    {
      id: "status",
      header: "Status",
      accessor: (r) => (
        <InvoiceStatusBadge status={r.status} />
      ),
      sortable: true,
      sortAccessor: (r) => r.status,
    },
    {
      id: "total",
      header: "Total",
      accessor: (r) => <span className="font-mono text-blue-700">{formatCurrency(r.totalAmount)}</span>,
      sortable: true,
      sortAccessor: (r) => r.totalAmount,
    },
    {
      id: "paid",
      header: "Paid",
      accessor: (r) => <span className="font-mono text-green-700">{formatCurrency(r.amountPaid)}</span>,
      sortable: true,
      sortAccessor: (r) => r.amountPaid,
    },
    {
      id: "balance",
      header: "Balance",
      accessor: (r) => <span className="font-mono text-red-700">{formatCurrency(r.balanceDue)}</span>,
      sortable: true,
      sortAccessor: (r) => r.balanceDue,
    },
    {
      id: "actions",
      header: "Actions",
      cell: (row) => (
        <div className="flex items-center justify-end gap-2">
          <Button
            size="sm"
            variant="destructive"
            onClick={() => onDeleteInvoice?.(row as any)}
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  const filtered = React.useMemo(
    () => applyFilters(invoices, filters),
    [invoices, filters]
  );
  const [selected, setSelected] = React.useState<React.Key[]>([]);

  return (
    <div className="space-y-3">
      <InvoiceTableFilterBar filters={filters} onChange={onFiltersChange} />
      <DataTable
        data={filtered}
        columns={columns}
        defaultSort={{ columnId: "date", direction: "desc" }}
        onSortChange={() => {}}
        rowKey={(row) => row.id}
        showColumnControls
        enableBulkSelection
        selectedRowKeys={selected}
        onSelectedRowKeysChange={setSelected}
        emptyState={
          <div className="rounded-md border border-dashed py-10 text-center">
            <div className="text-sm text-muted-foreground">
              No invoices found. Adjust filters or create a new invoice.
            </div>
            <div className="mt-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onFiltersChange({})}
              >
                Reset filters
              </Button>
            </div>
          </div>
        }
        bulkActions={
          selected.length > 0 ? (
            <div className="flex items-center gap-2 text-sm">
              <span className="text-muted-foreground">
                {selected.length} selected
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  /* placeholder for export */
                }}
              >
                Export
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelected([])}
              >
                Clear
              </Button>
            </div>
          ) : null
        }
        className="w-full"
      />
    </div>
  );
}

function InvoiceTableFilterBar({
  filters,
  onChange,
}: {
  filters: InvoiceTableFilters;
  onChange: (f: InvoiceTableFilters) => void;
}) {
  return (
    <div className="flex flex-wrap items-end gap-2">
      <div className="flex flex-col">
        <label className="text-xs text-muted-foreground">Patient</label>
        <Input
          value={filters.patient ?? ""}
          onChange={(e) => onChange({ ...filters, patient: e.target.value })}
          placeholder="Search by name"
          className="h-9 w-56"
        />
      </div>

      <div className="flex flex-col">
        <label className="text-xs text-muted-foreground">Status</label>
        <Select
          value={filters.status ?? "all"}
          onValueChange={(v) =>
            onChange({ ...filters, status: v as InvoiceTableFilters["status"] })
          }
        >
          <SelectTrigger className="h-9 w-40">
            <SelectValue placeholder="All" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="sent">Sent</SelectItem>
            <SelectItem value="partially_paid">Partially Paid</SelectItem>
            <SelectItem value="paid">Paid</SelectItem>
            <SelectItem value="overdue">Overdue</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <DateRangePicker
        label="Date range"
        from={filters.dateFrom}
        to={filters.dateTo}
        onChange={(from, to) =>
          onChange({ ...filters, dateFrom: from, dateTo: to })
        }
      />

      <div className="flex items-center gap-2">
        <div className="flex flex-col">
          <label className="text-xs text-muted-foreground">Min</label>
          <Input
            type="number"
            inputMode="decimal"
            className="h-9 w-28"
            placeholder="0"
            value={filters.amountMin ?? ""}
            onChange={(e) =>
              onChange({
                ...filters,
                amountMin: e.target.value ? Number(e.target.value) : undefined,
              })
            }
          />
        </div>
        <div className="flex flex-col">
          <label className="text-xs text-muted-foreground">Max</label>
          <Input
            type="number"
            inputMode="decimal"
            className="h-9 w-28"
            placeholder="1000"
            value={filters.amountMax ?? ""}
            onChange={(e) =>
              onChange({
                ...filters,
                amountMax: e.target.value ? Number(e.target.value) : undefined,
              })
            }
          />
        </div>
      </div>

      <Button
        variant="outline"
        className="ml-auto"
        onClick={() => onChange({})}
      >
        Reset
      </Button>
    </div>
  );
}

function DateRangePicker({
  label,
  from,
  to,
  onChange,
}: {
  label: string;
  from?: Date;
  to?: Date;
  onChange: (from?: Date, to?: Date) => void;
}) {
  const [open, setOpen] = React.useState(false);
  const [draftFrom, setDraftFrom] = React.useState<Date | undefined>(from);
  const [draftTo, setDraftTo] = React.useState<Date | undefined>(to);
  return (
    <div className="flex flex-col">
      <label className="text-xs text-muted-foreground">{label}</label>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" className="h-9 w-64 justify-between">
            <span className="truncate text-xs">
              {draftFrom ? formatDate(draftFrom) : "Start"} —{" "}
              {draftTo ? formatDate(draftTo) : "End"}
            </span>
          </Button>
        </PopoverTrigger>
        <PopoverContent align="start" className="w-auto p-2">
          <div className="flex items-start gap-2">
            <Calendar
              mode="single"
              selected={draftFrom}
              onSelect={setDraftFrom}
            />
            <Calendar mode="single" selected={draftTo} onSelect={setDraftTo} />
          </div>
          <div className="mt-2 flex items-center justify-end gap-2">
            <Button
              variant="ghost"
              onClick={() => {
                setDraftFrom(undefined);
                setDraftTo(undefined);
                onChange(undefined, undefined);
                setOpen(false);
              }}
            >
              Clear
            </Button>
            <Button
              onClick={() => {
                onChange(draftFrom, draftTo);
                setOpen(false);
              }}
            >
              Apply
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}

function applyFilters(
  invoices: InvoiceRow[],
  f: InvoiceTableFilters
): InvoiceRow[] {
  return invoices.filter((inv) => {
    if (
      f.patient &&
      !inv.patientName.toLowerCase().includes(f.patient.toLowerCase())
    )
      return false;
    if (f.status && f.status !== "all" && inv.status !== f.status) return false;
    if (f.dateFrom && new Date(inv.date) < f.dateFrom) return false;
    if (f.dateTo && new Date(inv.date) > f.dateTo) return false;
    if (typeof f.amountMin === "number" && inv.totalAmount < f.amountMin)
      return false;
    if (typeof f.amountMax === "number" && inv.totalAmount > f.amountMax)
      return false;
    return true;
  });
}

function parseNumber(v: string): number {
  const digits = v.replace(/[^0-9]/g, "");
  return Number(digits || 0);
}

function formatDate(value: string | Date): string {
  try {
    const date = typeof value === "string" ? new Date(value) : value;
    return new Intl.DateTimeFormat(undefined, {
      year: "numeric",
      month: "short",
      day: "2-digit",
    }).format(date);
  } catch {
    return String(value);
  }
}

// currency handled by shared helper

export default InvoiceTable;
