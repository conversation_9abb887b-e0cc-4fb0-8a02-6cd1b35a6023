"use client"

import * as React from "react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { formatCurrency } from "@/lib/currency"

export interface PaymentReceiptProps {
  serial: string
  receiptNumber?: string
  patient: { id: number | string; name: string }
  payment: { amount: number; date: string | Date; method?: string; notes?: string }
  balanceBefore?: number
  balanceAfter?: number
  clinic?: { name?: string; logoUrl?: string; addressLines?: string[] }
  onPrint?: () => void
  className?: string
}

export function PaymentReceipt({
  serial,
  receiptNumber,
  patient,
  payment,
  balanceBefore,
  balanceAfter,
  clinic,
  onPrint,
  className,
}: PaymentReceiptProps) {
  const handlePrint = () => {
    if (onPrint) return onPrint()
    if (typeof window !== "undefined") window.print()
  }

  return (
    <div
      className={cn(
        "bg-background text-foreground mx-auto w-full max-w-2xl rounded-lg border p-6 shadow-sm print:max-w-full print:border-0 print:p-0",
        className
      )}
    >
      {/* Header */}
      <div className="mb-6 flex items-center justify-between gap-4">
        <div className="flex items-center gap-3">
          {clinic?.logoUrl ? (
            // eslint-disable-next-line @next/next/no-img-element
            <img src={clinic.logoUrl} alt={clinic?.name ?? "Clinic"} className="h-10 w-10 rounded print:h-12 print:w-12" />
          ) : null}
          <div>
            <div className="text-base font-semibold">{clinic?.name ?? "Clinic"}</div>
            {clinic?.addressLines?.length ? (
              <div className="text-muted-foreground text-xs leading-tight">
                {clinic.addressLines.map((l, i) => (
                  <div key={i}>{l}</div>
                ))}
              </div>
            ) : null}
          </div>
        </div>
        <div className="text-right text-sm">
          <div>
            <span className="text-muted-foreground">Invoice</span> <span className="font-medium">{serial}</span>
          </div>
          {receiptNumber ? (
            <div>
              <span className="text-muted-foreground">Receipt</span> <span className="font-medium">{receiptNumber}</span>
            </div>
          ) : null}
          <div className="text-muted-foreground text-xs">{formatDate(payment.date)}</div>
        </div>
      </div>

      {/* Patient */}
      <div className="mb-4">
        <div className="text-sm font-medium">Patient</div>
        <div className="text-muted-foreground text-sm">{patient.name}</div>
      </div>

      {/* Payment details */}
      <div className="mb-4 grid grid-cols-1 gap-3 sm:grid-cols-2">
        <Detail label="Payment amount" value={formatCurrency(payment.amount)} />
        <Detail label="Payment method" value={payment.method ?? "—"} />
        {typeof balanceBefore === "number" ? (
          <Detail label="Balance before" value={formatCurrency(balanceBefore)} />
        ) : null}
        {typeof balanceAfter === "number" ? (
          <Detail label="Balance after" value={formatCurrency(balanceAfter)} />
        ) : null}
      </div>

      {payment.notes ? (
        <div className="mb-4">
          <div className="text-sm font-medium">Notes</div>
          <div className="text-muted-foreground whitespace-pre-wrap text-sm">{payment.notes}</div>
        </div>
      ) : null}

      {/* Footer */}
      <div className="mt-6 flex items-center justify-between">
        <div className="text-muted-foreground text-xs">Thank you for your payment.</div>
        <Button className="print:hidden" onClick={handlePrint}>
          Print Receipt
        </Button>
      </div>
    </div>
  )
}

function Detail({ label, value }: { label: string; value: React.ReactNode }) {
  return (
    <div className="rounded-md border bg-muted/30 px-3 py-2 text-sm">
      <div className="text-muted-foreground text-xs">{label}</div>
      <div className="font-medium">{value}</div>
    </div>
  )
}

function formatDate(value: string | Date): string {
  try {
    const d = typeof value === "string" ? new Date(value) : value
    return new Intl.DateTimeFormat(undefined, { year: "numeric", month: "short", day: "2-digit" }).format(d)
  } catch {
    return String(value)
  }
}

// currency handled by shared helper

export default PaymentReceipt


