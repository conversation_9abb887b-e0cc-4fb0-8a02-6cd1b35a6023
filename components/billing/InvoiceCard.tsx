"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { formatCurrency } from "@/lib/currency"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

export interface InvoiceWithDetails {
  id: number | string
  serial: string
  date: string | Date
  patient: { id: number | string; name: string }
  treatments: Array<{ id: number | string; procedureName: string; cost: number; status: string }>
  payments: Array<{ id: number | string; amount: number; date: string | Date }>
  totalAmount: number
  amountPaid: number
  balanceDue: number
}

export interface InvoiceCardProps {
  invoice: InvoiceWithDetails
  onViewDetails?: () => void
  onProcessPayment?: () => void
  onEdit?: () => void
  onSendInvoice?: () => void
  onPaymentSuccess?: (invoiceId: InvoiceWithDetails["id"]) => void
  className?: string
}

export function InvoiceCard({ invoice, onViewDetails, onProcessPayment, onEdit, onSendInvoice, onPaymentSuccess, className }: InvoiceCardProps) {
  const formattedDate = formatDate(invoice.date)
  const total = formatCurrency(invoice.totalAmount)
  const paid = formatCurrency(invoice.amountPaid)
  const balance = formatCurrency(invoice.balanceDue)

  const statusBadge = (
    <span className={cn(
      "rounded-full px-2 py-0.5 text-[11px] font-semibold",
      invoice.balanceDue <= 0 ? "bg-emerald-600 text-white" : "bg-muted text-foreground"
    )}>
      {invoice.balanceDue <= 0 ? "PAID" : "DUE"}
    </span>
  )

  return (
    <Card className={cn("@container", className)}>
      <CardHeader className="border-b">
        <div className="flex items-center justify-between gap-3">
          <div>
            <CardTitle>Invoice #{invoice.serial}</CardTitle>
            <CardDescription>{formattedDate}</CardDescription>
          </div>
          <div className="text-right text-sm">
            <div>
              <span className="text-muted-foreground">Total</span> <span className="font-medium">{total}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Paid</span> <span className="font-medium">{paid}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Balance</span> <span className="font-semibold">{balance}</span>
            </div>
            <div className="mt-1">{statusBadge}</div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="py-4">
        <div className="grid grid-cols-1 items-start gap-4 sm:grid-cols-2">
          <div className="space-y-1">
            <div className="text-sm font-medium">Patient</div>
            <div className="text-muted-foreground text-sm">{invoice.patient.name}</div>
          </div>
          <div className="space-y-1">
            <div className="text-sm font-medium">Treatments</div>
            <div className="text-muted-foreground text-sm">{invoice.treatments.length} items</div>
          </div>
        </div>
      </CardContent>

      <CardFooter className="justify-end border-t">
        <div className="flex flex-wrap items-center gap-2">
          {invoice.balanceDue > 0 && (
            <Button onClick={() => { onProcessPayment?.(); onPaymentSuccess?.(invoice.id) }} disabled={!onProcessPayment}>
              Process Payment
            </Button>
          )}
          <Button variant="outline" onClick={onEdit} disabled={!onEdit}>
            Edit
          </Button>
          <Button variant="secondary" onClick={onViewDetails} disabled={!onViewDetails}>
            View Details
          </Button>
          {invoice.serial && onSendInvoice && (
            <Button variant="secondary" onClick={onSendInvoice}>
              Send Invoice
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  )
}

function formatDate(value: string | Date): string {
  try {
    const date = typeof value === "string" ? new Date(value) : value
    return new Intl.DateTimeFormat(undefined, {
      year: "numeric",
      month: "short",
      day: "2-digit",
    }).format(date)
  } catch {
    return String(value)
  }
}

// currency handled by shared helper

export default InvoiceCard


