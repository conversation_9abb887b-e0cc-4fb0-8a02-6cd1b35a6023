enum TreatmentStatus {
  PENDING
  COMPLETED
}

model Treatment {
  id              Int             @id @default(autoincrement())
  tenantId        String
  tenant          Tenant          @relation(fields: [tenantId], references: [id])
  
  // Direct relationship to finding (no diagnosis layer)
  findingId       Int
  finding         Finding         @relation(fields: [findingId], references: [id])
  
  // Essential treatment data
  procedureName   String
  cost            Decimal
  status          TreatmentStatus @default(PENDING)

  // Link to invoice as a line item (optional until invoiced)
  invoiceId       Int?
  invoice         Invoice?        @relation(fields: [invoiceId], references: [id])
  
  // Completion tracking
  completedDate   DateTime?
  completedById   Int?
  completedBy     User?           @relation("CompletedBy", fields: [completedById], references: [id])
  
  // Basic audit fields
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  createdById     Int?
  updatedById     Int?
  
  @@index([tenantId, findingId])
  @@index([tenantId, status])
  @@index([invoiceId])
}