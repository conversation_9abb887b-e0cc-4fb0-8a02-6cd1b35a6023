/*
  Warnings:

  - You are about to drop the column `invoiceNumber` on the `Invoice` table. All the data in the column will be lost.

*/
-- CreateTable
CREATE TABLE "CaseSheetSerial" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "tenantId" TEXT NOT NULL,
    "serial" TEXT NOT NULL,
    CONSTRAINT "CaseSheetSerial_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "InvoiceSerial" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "tenantId" TEXT NOT NULL,
    "serial" TEXT NOT NULL,
    CONSTRAINT "InvoiceSerial_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "PaymentSerial" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "tenantId" TEXT NOT NULL,
    "serial" TEXT NOT NULL,
    CONSTRAINT "PaymentSerial_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_CaseSheet" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "tenantId" TEXT NOT NULL,
    "serial" TEXT NOT NULL DEFAULT '',
    "patientId" INTEGER NOT NULL,
    "clinicalNotes" TEXT,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "lastVisitDate" DATETIME,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "createdById" INTEGER,
    "updatedById" INTEGER,
    CONSTRAINT "CaseSheet_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "CaseSheet_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "Patient" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_CaseSheet" ("clinicalNotes", "createdAt", "createdById", "id", "lastVisitDate", "patientId", "status", "tenantId", "updatedAt", "updatedById") SELECT "clinicalNotes", "createdAt", "createdById", "id", "lastVisitDate", "patientId", "status", "tenantId", "updatedAt", "updatedById" FROM "CaseSheet";
DROP TABLE "CaseSheet";
ALTER TABLE "new_CaseSheet" RENAME TO "CaseSheet";
CREATE UNIQUE INDEX "CaseSheet_patientId_key" ON "CaseSheet"("patientId");
CREATE INDEX "CaseSheet_tenantId_patientId_idx" ON "CaseSheet"("tenantId", "patientId");
CREATE INDEX "CaseSheet_tenantId_status_idx" ON "CaseSheet"("tenantId", "status");
CREATE TABLE "new_Invoice" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "tenantId" TEXT NOT NULL,
    "serial" TEXT NOT NULL DEFAULT '',
    "patientId" INTEGER NOT NULL,
    "invoiceDate" DATETIME NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'DRAFT',
    "totalAmount" DECIMAL NOT NULL DEFAULT 0.00,
    "amountPaid" DECIMAL NOT NULL DEFAULT 0.00,
    "balanceDue" DECIMAL NOT NULL DEFAULT 0.00,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "createdById" INTEGER,
    "updatedById" INTEGER,
    CONSTRAINT "Invoice_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "Invoice_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "Patient" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_Invoice" ("amountPaid", "balanceDue", "createdAt", "createdById", "id", "invoiceDate", "patientId", "status", "tenantId", "totalAmount", "updatedAt", "updatedById") SELECT "amountPaid", "balanceDue", "createdAt", "createdById", "id", "invoiceDate", "patientId", "status", "tenantId", "totalAmount", "updatedAt", "updatedById" FROM "Invoice";
DROP TABLE "Invoice";
ALTER TABLE "new_Invoice" RENAME TO "Invoice";
CREATE INDEX "Invoice_tenantId_patientId_idx" ON "Invoice"("tenantId", "patientId");
CREATE INDEX "Invoice_tenantId_status_idx" ON "Invoice"("tenantId", "status");
CREATE UNIQUE INDEX "Invoice_tenantId_serial_key" ON "Invoice"("tenantId", "serial");
CREATE TABLE "new_Payment" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "tenantId" TEXT NOT NULL,
    "serial" TEXT NOT NULL DEFAULT '',
    "patientId" INTEGER NOT NULL,
    "invoiceId" INTEGER,
    "amount" DECIMAL NOT NULL,
    "paymentDate" DATETIME NOT NULL,
    "paymentMethod" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'COMPLETED',
    "notes" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "createdById" INTEGER,
    "updatedById" INTEGER,
    CONSTRAINT "Payment_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "Payment_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "Patient" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "Payment_invoiceId_fkey" FOREIGN KEY ("invoiceId") REFERENCES "Invoice" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);
INSERT INTO "new_Payment" ("amount", "createdAt", "createdById", "id", "invoiceId", "notes", "patientId", "paymentDate", "paymentMethod", "status", "tenantId", "updatedAt", "updatedById") SELECT "amount", "createdAt", "createdById", "id", "invoiceId", "notes", "patientId", "paymentDate", "paymentMethod", "status", "tenantId", "updatedAt", "updatedById" FROM "Payment";
DROP TABLE "Payment";
ALTER TABLE "new_Payment" RENAME TO "Payment";
CREATE INDEX "Payment_tenantId_patientId_idx" ON "Payment"("tenantId", "patientId");
CREATE INDEX "Payment_tenantId_invoiceId_idx" ON "Payment"("tenantId", "invoiceId");
CREATE INDEX "Payment_tenantId_paymentDate_idx" ON "Payment"("tenantId", "paymentDate");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
