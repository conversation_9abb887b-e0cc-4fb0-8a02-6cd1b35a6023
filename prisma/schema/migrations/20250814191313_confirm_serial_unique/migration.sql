/*
  Warnings:

  - A unique constraint covering the columns `[tenantId,serial]` on the table `CaseSheet` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[tenantId,serial]` on the table `CaseSheetSerial` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[tenantId,serial]` on the table `InvoiceSerial` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[tenantId,serial]` on the table `Payment` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[tenantId,serial]` on the table `PaymentSerial` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateIndex
CREATE UNIQUE INDEX "CaseSheet_tenantId_serial_key" ON "CaseSheet"("tenantId", "serial");

-- CreateIndex
CREATE UNIQUE INDEX "CaseSheetSerial_tenantId_serial_key" ON "CaseSheetSerial"("tenantId", "serial");

-- CreateIndex
CREATE UNIQUE INDEX "InvoiceSerial_tenantId_serial_key" ON "InvoiceSerial"("tenantId", "serial");

-- CreateIndex
CREATE UNIQUE INDEX "Payment_tenantId_serial_key" ON "Payment"("tenantId", "serial");

-- CreateIndex
CREATE UNIQUE INDEX "PaymentSerial_tenantId_serial_key" ON "PaymentSerial"("tenantId", "serial");
