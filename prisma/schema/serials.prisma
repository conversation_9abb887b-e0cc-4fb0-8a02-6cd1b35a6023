model CaseSheetSerial {
    id Int @id @default(autoincrement())
    tenant Tenant @relation(fields: [tenantId], references: [id])
    tenantId String
    serial String
    @@unique([tenantId, serial])
}

model InvoiceSerial {
    id Int @id @default(autoincrement())
    tenant Tenant @relation(fields: [tenantId], references: [id])
    tenantId String
    serial String
    @@unique([tenantId, serial])
}

model PaymentSerial {
    id Int @id @default(autoincrement())
    tenant Tenant @relation(fields: [tenantId], references: [id])
    tenantId String
    serial String
    @@unique([tenantId, serial])
}