enum CaseSheetStatus {
  ACTIVE
  INACTIVE
}

model CaseSheet {
  id              Int             @id @default(autoincrement())
  tenantId        String
  tenant          Tenant          @relation(fields: [tenantId], references: [id])
  serial          String          @default("")
  // One-to-one relationship with Patient
  patientId       Int             @unique
  patient         Patient         @relation(fields: [patientId], references: [id])
  
  // Clinical information
  clinicalNotes   String?
  status          CaseSheetStatus @default(ACTIVE)
  lastVisitDate   DateTime?
  
  // Audit fields
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  createdById     Int?
  updatedById     Int?
  
  // Relations
  teeth           Tooth[]
  
  @@index([tenantId, patientId])
  @@index([tenantId, status])
  @@unique([tenantId, serial])
}