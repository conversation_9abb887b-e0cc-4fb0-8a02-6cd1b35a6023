model Tenant {
  id        String   @id @default(uuid())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Business Information (optional)
  phoneNumber String?
  address     String?
  logoImage   String? // URL or path to logo image
  
  // Relations
  users               User[]
  patients            Patient[]
  appointments        Appointment[]
  caseSheets          CaseSheet[]
  teeth               Tooth[]
  findings            Finding[]
  treatments          Treatment[]
  invoices            Invoice[]
  payments            Payment[]
  caseSheetSerial     CaseSheetSerial[]
  invoiceSerial       InvoiceSerial[]
  paymentSerial       PaymentSerial[]
}