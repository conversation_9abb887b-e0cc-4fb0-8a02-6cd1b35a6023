import { NextRequest, NextResponse } from "next/server";
import { AuthService } from "@/lib/auth/services/auth-service";
import { TenantContextManager } from "@/lib/tenant-context";

/**
 * Next.js Middleware for Authentication and Tenant Context
 * Requirements: 3.4, 3.6, 4.3, 8.1, 8.2
 * 
 * This middleware runs on every request and:
 * 1. Validates authentication for protected routes
 * 2. Sets tenant context automatically for authenticated users
 * 3. Redirects unauthenticated users to login
 * 4. Handles role-based access control
 */

// Define route patterns
const PUBLIC_ROUTES = [
  "/",
  "/auth/login",
  "/auth/register",
  "/api/auth/login",
  "/api/auth/register",
  "/api/auth/logout",
  "/favicon.ico",
  "/_next",
  "/static",
];

const API_ROUTES = [
  "/api/",
];

const ADMIN_ROUTES = [
  "/dashboard/admin",
];

const AUTH_ROUTES = [
  "/auth/",
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  
  console.log(`[MIDDLEWARE] Processing request: ${request.method} ${pathname}`);

  // Skip middleware for public routes
  if (PUBLIC_ROUTES.some(route => pathname.startsWith(route))) {
    console.log(`[MIDDLEWARE] Public route, skipping auth check: ${pathname}`);
    return NextResponse.next();
  }

  try {
    // Validate session
    const sessionValidation = await AuthService.validateSession();
    const isAuthenticated = sessionValidation.isValid && sessionValidation.session;
    
    console.log(`[MIDDLEWARE] Authentication status: ${isAuthenticated ? 'authenticated' : 'not authenticated'}`);

    // Handle unauthenticated users
    if (!isAuthenticated) {
      console.log(`[MIDDLEWARE] Unauthenticated user accessing: ${pathname}`);
      
      // For API routes, return 401
      if (API_ROUTES.some(route => pathname.startsWith(route))) {
        return NextResponse.json(
          {
            success: false,
            message: "Authentication required",
            errors: ["Please log in to access this resource"],
          },
          { status: 401 }
        );
      }
      
      // For page routes, redirect to login
      const loginUrl = new URL("/auth/login", request.url);
      loginUrl.searchParams.set("redirect", pathname);
      console.log(`[MIDDLEWARE] Redirecting to login: ${loginUrl.toString()}`);
      return NextResponse.redirect(loginUrl);
    }

    const session = sessionValidation.session!;
    console.log(`[MIDDLEWARE] Authenticated user: ${session.userId} (${session.userType})`);

    // Set tenant context for authenticated users
    const tenantContext = TenantContextManager.createContextFromAuthSession(
      session,
      undefined, // sessionId is part of the JWT
      undefined  // We'll get tenant name later if needed
    );

    // Set global tenant context for this request
    TenantContextManager.setGlobalContext(tenantContext);
    console.log(`[MIDDLEWARE] Tenant context set: ${JSON.stringify(tenantContext)}`);

    // Handle role-based access control for admin routes
    if (ADMIN_ROUTES.some(route => pathname.startsWith(route))) {
      const isAdmin = session.userType === "SUPERUSER" || session.userType === "ADMIN";
      
      if (!isAdmin) {
        console.log(`[MIDDLEWARE] Non-admin user accessing admin route: ${session.userType}`);
        
        // For API routes, return 403
        if (API_ROUTES.some(route => pathname.startsWith(route))) {
          return NextResponse.json(
            {
              success: false,
              message: "Insufficient permissions",
              errors: ["You don't have permission to access this resource"],
            },
            { status: 403 }
          );
        }
        
        // For page routes, redirect to unauthorized or dashboard
        const dashboardUrl = new URL("/dashboard", request.url);
        console.log(`[MIDDLEWARE] Redirecting non-admin to dashboard: ${dashboardUrl.toString()}`);
        return NextResponse.redirect(dashboardUrl);
      }
    }

    // Redirect authenticated users away from auth routes
    if (AUTH_ROUTES.some(route => pathname.startsWith(route))) {
      console.log(`[MIDDLEWARE] Authenticated user accessing auth route, redirecting to dashboard`);
      const dashboardUrl = new URL("/dashboard", request.url);
      return NextResponse.redirect(dashboardUrl);
    }

    // Add tenant context to request headers for API routes
    if (API_ROUTES.some(route => pathname.startsWith(route))) {
      const requestHeaders = new Headers(request.headers);
      requestHeaders.set("x-tenant-id", session.tenantId);
      requestHeaders.set("x-user-id", session.userId);
      requestHeaders.set("x-user-type", session.userType);
      requestHeaders.set("x-tenant-context", JSON.stringify(tenantContext));
      
      console.log(`[MIDDLEWARE] Added tenant context headers for API route`);
      
      return NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      });
    }

    // Continue with the request
    console.log(`[MIDDLEWARE] Request authorized, continuing: ${pathname}`);
    return NextResponse.next();

  } catch (error) {
    console.error("[MIDDLEWARE] Error in middleware:", error);
    
    // For API routes, return error response
    if (API_ROUTES.some(route => pathname.startsWith(route))) {
      return NextResponse.json(
        {
          success: false,
          message: "Authentication error",
          errors: ["An error occurred during authentication"],
        },
        { status: 500 }
      );
    }
    
    // For page routes, redirect to login
    const loginUrl = new URL("/auth/login", request.url);
    return NextResponse.redirect(loginUrl);
  }
}

/**
 * Configure which routes this middleware should run on
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (assuming they're in the public directory)
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
