"use client"

import * as React from "react"

export interface UsePinchZoomOptions {
  minScale?: number
  maxScale?: number
}

export interface UsePinchZoomResult {
  ref: React.RefObject<HTMLDivElement>
  style: React.CSSProperties
  handlers: {
    onTouchStart: (e: React.TouchEvent) => void
    onTouchMove: (e: React.TouchEvent) => void
    onTouchEnd: (e: React.TouchEvent) => void
  }
  scale: number
  reset: () => void
}

export function usePinchZoom(options: UsePinchZoomOptions = {}): UsePinchZoomResult {
  const { minScale = 1, maxScale = 3 } = options

  const containerRef = React.useRef<HTMLDivElement>(null)
  const [scale, setScale] = React.useState<number>(1)
  const [translate, setTranslate] = React.useState<{ x: number; y: number }>({ x: 0, y: 0 })

  const gestureRef = React.useRef<{
    mode: "none" | "pinch" | "pan"
    startDistance: number
    startScale: number
    lastTouch: { x: number; y: number } | null
  }>({ mode: "none", startDistance: 0, startScale: 1, lastTouch: null })

  const clamp = (value: number, min: number, max: number) => Math.min(max, Math.max(min, value))

  const getDistance = (a: Touch, b: Touch) => {
    const dx = a.clientX - b.clientX
    const dy = a.clientY - b.clientY
    return Math.hypot(dx, dy)
  }

  const reset = React.useCallback(() => {
    setScale(1)
    setTranslate({ x: 0, y: 0 })
    gestureRef.current = { mode: "none", startDistance: 0, startScale: 1, lastTouch: null }
  }, [])

  const onTouchStart = React.useCallback((e: React.TouchEvent) => {
    if (e.touches.length === 2) {
      const [t1, t2] = [e.touches[0], e.touches[1]]
      gestureRef.current.mode = "pinch"
      gestureRef.current.startDistance = getDistance(t1, t2)
      gestureRef.current.startScale = scale
      gestureRef.current.lastTouch = null
    } else if (e.touches.length === 1) {
      const t = e.touches[0]
      gestureRef.current.mode = scale > 1 ? "pan" : "none"
      gestureRef.current.lastTouch = { x: t.clientX, y: t.clientY }
    }
  }, [scale])

  const onTouchMove = React.useCallback((e: React.TouchEvent) => {
    if (gestureRef.current.mode === "pinch" && e.touches.length === 2) {
      e.preventDefault()
      const [t1, t2] = [e.touches[0], e.touches[1]]
      const distance = getDistance(t1, t2)
      const factor = distance / (gestureRef.current.startDistance || distance)
      const nextScale = clamp(gestureRef.current.startScale * factor, minScale, maxScale)
      setScale(nextScale)
    } else if (gestureRef.current.mode === "pan" && e.touches.length === 1) {
      e.preventDefault()
      const t = e.touches[0]
      const last = gestureRef.current.lastTouch
      if (last) {
        const dx = t.clientX - last.x
        const dy = t.clientY - last.y
        setTranslate((prev) => ({ x: prev.x + dx, y: prev.y + dy }))
      }
      gestureRef.current.lastTouch = { x: t.clientX, y: t.clientY }
    }
  }, [maxScale, minScale])

  const onTouchEnd = React.useCallback((e: React.TouchEvent) => {
    if (e.touches.length === 0) {
      // Snap back if scale very close to 1
      if (scale < 1.02) {
        reset()
      }
      gestureRef.current = { mode: "none", startDistance: 0, startScale: 1, lastTouch: null }
    } else if (e.touches.length === 1) {
      // If one touch remains, switch to pan if zoomed
      gestureRef.current.mode = scale > 1 ? "pan" : "none"
      gestureRef.current.lastTouch = { x: e.touches[0].clientX, y: e.touches[0].clientY }
    }
  }, [reset, scale])

  const style = React.useMemo<React.CSSProperties>(() => ({
    transform: `translate3d(${translate.x}px, ${translate.y}px, 0) scale(${scale})`,
    transformOrigin: "center center",
    touchAction: "none",
    willChange: "transform",
  }), [scale, translate.x, translate.y])

  return {
    ref: containerRef,
    style,
    handlers: { onTouchStart, onTouchMove, onTouchEnd },
    scale,
    reset,
  }
}

export default usePinchZoom


