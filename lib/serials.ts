// Deliberately avoid a static import of `prisma` to prevent circular dependencies
// with files that import from `lib/prisma-extensions`. We'll dynamically import
// `./prisma` only when needed.

const MAX_RETRIES = 5

async function generateWithRetry(
  tenantId: string,
  table: "caseSheetSerial" | "invoiceSerial" | "paymentSerial",
  txOrClient?: any
): Promise<string> {
  const run = async (client: any): Promise<string> => {
    for (let attempt = 0; attempt < MAX_RETRIES; attempt++) {
      const count = await client[table].count({ where: { tenantId } })
      const serial = String(count + 1)
      try {
        await client[table].create({ data: { tenantId, serial } })
        return serial
      } catch (error: any) {
        if (error?.code === "P2002") {
          // Unique constraint violation; retry
          if (attempt === MAX_RETRIES - 1) {
            break
          }
          continue
        }
        throw error
      }
    }
    throw new Error(
      `Failed to generate unique serial for ${table} after ${MAX_RETRIES} attempts`
    )
  }

  if (txOrClient) {
    return run(txOrClient)
  }

  const { prisma } = await import("./prisma")
  return prisma.$transaction(async (trx: any) => run(trx))
}

export const generateCaseSheetSerial = async (tenantId: string, tx?: any): Promise<string> => {
  return generateWithRetry(tenantId, "caseSheetSerial", tx)
}

export const generateInvoiceSerial = async (tenantId: string, tx?: any): Promise<string> => {
  return generateWithRetry(tenantId, "invoiceSerial", tx)
}

export const generatePaymentSerial = async (tenantId: string, tx?: any): Promise<string> => {
  return generateWithRetry(tenantId, "paymentSerial", tx)
}