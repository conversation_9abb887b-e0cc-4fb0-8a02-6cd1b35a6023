import { prisma } from "../../prisma";
import { TenantContextManager } from "../../tenant-context";
import { 
  AdminUserCreationData, 
  UserCreationR<PERSON>ult, 
  AuthR<PERSON>ult, 
  AuthUser,
  UserType,
  AuthenticationError 
} from "../types";
import { PasswordUtils, SanitizationUtils, ValidationUtils } from "../utils";

/**
 * UserService - Handles user management and authentication operations
 * Requirements: 2.1, 2.2, 2.4, 3.2, 4.1
 */
export class UserService {
  /**
   * Create admin user with superuser privileges
   * Requirements: 2.1, 2.2
   */
  static async createAdminUser(data: AdminUserCreationData): Promise<UserCreationResult> {
    try {
      // Sanitize input data
      const sanitizedData = {
        username: SanitizationUtils.sanitizeString(data.username),
        email: SanitizationUtils.sanitizeEmail(data.email),
        firstName: SanitizationUtils.sanitizeString(data.firstName),
        lastName: SanitizationUtils.sanitizeString(data.lastName),
        password: data.password, // Don't sanitize password
        tenantId: data.tenantId,
      };

      // Validate input data
      const errors: string[] = [];
      
      if (!ValidationUtils.isValidUsername(sanitizedData.username)) {
        errors.push("Username can only contain letters, numbers, and underscores (3-30 characters)");
      }
      
      if (!ValidationUtils.isValidEmail(sanitizedData.email)) {
        errors.push("Valid email address is required");
      }
      
      if (!sanitizedData.firstName || sanitizedData.firstName.length < 1) {
        errors.push("First name is required");
      }
      
      if (!sanitizedData.lastName || sanitizedData.lastName.length < 1) {
        errors.push("Last name is required");
      }

      // Validate password strength
      const passwordValidation = PasswordUtils.validatePasswordStrength(sanitizedData.password);
      if (!passwordValidation.isValid) {
        errors.push(...passwordValidation.errors);
      }

      if (errors.length > 0) {
        return {
          success: false,
          message: "Validation failed",
          errors,
        };
      }

      // Check for existing user with same username or email in tenant
      const existingUser = await this.checkUserExists(
        sanitizedData.username,
        sanitizedData.email,
        sanitizedData.tenantId
      );

      if (existingUser.exists) {
        return {
          success: false,
          message: "User already exists",
          errors: existingUser.errors,
        };
      }

      // Hash password
      const hashedPassword = await PasswordUtils.hashPassword(sanitizedData.password);

      // Create user with tenant context
      const user = await TenantContextManager.runWithContext(
        { tenantId: sanitizedData.tenantId },
        async () => {
          return prisma.user.create({
            data: {
              username: sanitizedData.username,
              email: sanitizedData.email,
              firstName: sanitizedData.firstName,
              lastName: sanitizedData.lastName,
              password: hashedPassword,
              userType: "ADMIN", // Admin users get ADMIN type (closest to superuser)
              isActive: true,
              tenantId: sanitizedData.tenantId,
            },
          });
        }
      );

      return {
        success: true,
        message: "Admin user created successfully",
        userId: user.id.toString(),
      };
    } catch (error) {
      console.error("Error creating admin user:", error);
      return {
        success: false,
        message: "Failed to create admin user",
        errors: ["An unexpected error occurred"],
      };
    }
  }

  /**
   * Authenticate user with credentials
   * Requirements: 3.2, 2.4
   */
  static async authenticateUser(
    username: string,
    password: string,
    tenantId?: string
  ): Promise<AuthResult> {
    try {
      // Sanitize input
      const sanitizedUsername = SanitizationUtils.sanitizeString(username);

      if (!sanitizedUsername || !password) {
        return {
          success: false,
          message: "Username and password are required",
        };
      }

      // Find user by username or email across all tenants if no tenantId provided
      let user;
      
      if (tenantId) {
        // Search within specific tenant
        user = await TenantContextManager.runWithContext(
          { tenantId },
          async () => {
            return prisma.user.findFirst({
              where: {
                OR: [
                  { username: sanitizedUsername },
                  { email: sanitizedUsername },
                ],
                isActive: true,
                tenantId,
              },
            });
          }
        );
      } else {
        // Search across all tenants (for login without tenant context)
        user = await prisma.$extends({}).bypassTenant(async () => {
          return prisma.user.findFirst({
            where: {
              OR: [
                { username: sanitizedUsername },
                { email: sanitizedUsername },
              ],
              isActive: true,
            },
          });
        });
      }

      if (!user) {
        return {
          success: false,
          message: "Invalid credentials",
        };
      }

      // Verify password
      const isPasswordValid = await PasswordUtils.verifyPassword(password, user.password);
      if (!isPasswordValid) {
        return {
          success: false,
          message: "Invalid credentials",
        };
      }

      // Check if user account is active
      if (!user.isActive) {
        return {
          success: false,
          message: "Account is inactive",
        };
      }

      // Create auth user object
      const authUser: AuthUser = {
        id: user.id.toString(),
        username: user.username || "",
        email: user.email || "",
        firstName: user.firstName || "",
        lastName: user.lastName || "",
        userType: user.userType as UserType,
        tenantId: user.tenantId,
        isActive: user.isActive,
      };

      return {
        success: true,
        message: "Authentication successful",
        user: authUser,
      };
    } catch (error) {
      console.error("Error authenticating user:", error);
      return {
        success: false,
        message: "Authentication failed",
        errors: ["An unexpected error occurred"],
      };
    }
  }

  /**
   * Check if user exists with given username or email in tenant
   * Requirements: 2.5
   */
  static async checkUserExists(
    username: string,
    email: string,
    tenantId: string
  ): Promise<{ exists: boolean; errors: string[] }> {
    try {
      const errors: string[] = [];

      // Check username uniqueness within tenant
      const existingByUsername = await TenantContextManager.runWithContext(
        { tenantId },
        async () => {
          return prisma.user.findFirst({
            where: {
              username,
              tenantId,
            },
          });
        }
      );

      if (existingByUsername) {
        errors.push("Username already exists in this clinic");
      }

      // Check email uniqueness within tenant
      const existingByEmail = await TenantContextManager.runWithContext(
        { tenantId },
        async () => {
          return prisma.user.findFirst({
            where: {
              email,
              tenantId,
            },
          });
        }
      );

      if (existingByEmail) {
        errors.push("Email already exists in this clinic");
      }

      return {
        exists: errors.length > 0,
        errors,
      };
    } catch (error) {
      console.error("Error checking user existence:", error);
      return {
        exists: true, // Fail safe - assume exists to prevent duplicates
        errors: ["Unable to verify user uniqueness"],
      };
    }
  }

  /**
   * Get user by ID
   */
  static async getUserById(userId: string, tenantId: string): Promise<AuthUser | null> {
    try {
      const user = await TenantContextManager.runWithContext(
        { tenantId },
        async () => {
          return prisma.user.findUnique({
            where: { 
              id: parseInt(userId),
              tenantId,
            },
          });
        }
      );

      if (!user) {
        return null;
      }

      return {
        id: user.id.toString(),
        username: user.username || "",
        email: user.email || "",
        firstName: user.firstName || "",
        lastName: user.lastName || "",
        userType: user.userType as UserType,
        tenantId: user.tenantId,
        isActive: user.isActive,
      };
    } catch (error) {
      console.error("Error fetching user:", error);
      return null;
    }
  }

  /**
   * Update user password
   * Requirements: 2.4, 4.1
   */
  static async updatePassword(
    userId: string,
    currentPassword: string,
    newPassword: string,
    tenantId: string
  ): Promise<UserCreationResult> {
    try {
      // Validate new password strength
      const passwordValidation = PasswordUtils.validatePasswordStrength(newPassword);
      if (!passwordValidation.isValid) {
        return {
          success: false,
          message: "Password does not meet requirements",
          errors: passwordValidation.errors,
        };
      }

      // Get current user
      const user = await TenantContextManager.runWithContext(
        { tenantId },
        async () => {
          return prisma.user.findUnique({
            where: { 
              id: parseInt(userId),
              tenantId,
            },
          });
        }
      );

      if (!user) {
        throw new AuthenticationError("User not found", "USER_NOT_FOUND");
      }

      // Verify current password
      const isCurrentPasswordValid = await PasswordUtils.verifyPassword(
        currentPassword,
        user.password
      );

      if (!isCurrentPasswordValid) {
        return {
          success: false,
          message: "Current password is incorrect",
          errors: ["Invalid current password"],
        };
      }

      // Hash new password
      const hashedNewPassword = await PasswordUtils.hashPassword(newPassword);

      // Update password
      await TenantContextManager.runWithContext(
        { tenantId },
        async () => {
          return prisma.user.update({
            where: { 
              id: parseInt(userId),
              tenantId,
            },
            data: { password: hashedNewPassword },
          });
        }
      );

      return {
        success: true,
        message: "Password updated successfully",
        userId,
      };
    } catch (error) {
      console.error("Error updating password:", error);
      
      if (error instanceof AuthenticationError) {
        throw error;
      }
      
      return {
        success: false,
        message: "Failed to update password",
        errors: ["An unexpected error occurred"],
      };
    }
  }

  /**
   * Deactivate user account
   * Requirements: 3.6
   */
  static async deactivateUser(userId: string, tenantId: string): Promise<UserCreationResult> {
    try {
      await TenantContextManager.runWithContext(
        { tenantId },
        async () => {
          return prisma.user.update({
            where: { 
              id: parseInt(userId),
              tenantId,
            },
            data: { isActive: false },
          });
        }
      );

      return {
        success: true,
        message: "User account deactivated successfully",
        userId,
      };
    } catch (error) {
      console.error("Error deactivating user:", error);
      return {
        success: false,
        message: "Failed to deactivate user account",
        errors: ["An unexpected error occurred"],
      };
    }
  }
}