/**
 * TypeScript interfaces for authentication data types
 */

// User types from the existing schema
export type UserType = "SUPERUSER" | "ADMIN" | "DENTIST" | "HYGIENIST" | "ASSISTANT" | "RECEPTIONIST";

// Authentication result types
export interface AuthResult {
  success: boolean;
  message: string;
  user?: AuthUser;
  errors?: string[];
}

export interface AuthUser {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  userType: UserType;
  tenantId: string;
  isActive: boolean;
}

// Session management types
export interface SessionInfo {
  userId: string;
  tenantId: string;
  username: string;
  userType: UserType;
  isActive: boolean;
  createdAt: Date;
  expiresAt: Date;
}

export interface SessionConfig {
  maxAge: number; // in seconds
  secure: boolean;
  httpOnly: boolean;
  sameSite: 'strict' | 'lax' | 'none';
}

// Tenant creation types
export interface TenantCreationData {
  name: string;
  address: string;
  phone: string;
  email: string;
}

export interface TenantCreationResult {
  success: boolean;
  message: string;
  tenantId?: string;
  errors?: string[];
}

// User creation types
export interface AdminUserCreationData {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  tenantId: string;
}

export interface UserCreationResult {
  success: boolean;
  message: string;
  userId?: string;
  errors?: string[];
}

// Rate limiting types
export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxAttempts: number; // Maximum attempts per window
  blockDurationMs: number; // How long to block after exceeding limit
}

export interface RateLimitInfo {
  attempts: number;
  windowStart: Date;
  isBlocked: boolean;
  blockUntil?: Date;
}

// Error types
export class AuthenticationError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class ValidationError extends Error {
  constructor(message: string, public errors: string[]) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class TenantError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'TenantError';
  }
}

export class RateLimitError extends Error {
  constructor(message: string, public retryAfter: number) {
    super(message);
    this.name = 'RateLimitError';
  }
}

// API request/response types
export interface ApiRequest<T = unknown> {
  body: T;
  headers: Record<string, string>;
  ip?: string;
  userAgent?: string;
}

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message: string;
  errors?: string[];
  statusCode: number;
}

// Middleware types
export interface AuthMiddlewareOptions {
  requireAuth?: boolean;
  allowedUserTypes?: UserType[];
  requireTenant?: boolean;
}

export interface AuthContext {
  user?: AuthUser;
  session?: SessionInfo;
  tenant?: {
    id: string;
    name: string;
  };
}