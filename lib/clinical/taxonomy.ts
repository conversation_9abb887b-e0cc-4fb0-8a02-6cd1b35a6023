// Centralized clinical taxonomy for findings and treatment procedures
// Values are the exact labels to be saved in the database

export type TaxonomyItem = { value: string; label: string }
export type TaxonomyGroup = { label: string; items: TaxonomyItem[] }

// Finding categories and subcategories
export const FINDING_CATEGORIES: TaxonomyItem[] = [
  { value: 'Dental Caries', label: 'Dental Caries' },
  { value: 'Periodontal Disease', label: 'Periodontal Disease' },
  { value: 'Endodontic', label: 'Endodontic' },
  { value: 'Oral Pathology', label: 'Oral Pathology' },
  { value: 'Orthodontic', label: 'Orthodontic' },
  { value: 'Oral Surgery', label: 'Oral Surgery' },
  { value: 'Prosthodontic', label: 'Prosthodontic' },
  { value: 'Preventive', label: 'Preventive' },
  { value: 'TMJ Disorders', label: 'TMJ Disorders' },
  { value: 'Trauma', label: 'Trauma' },
  { value: 'Congenital Anomalies', label: 'Congenital Anomalies' },
  { value: 'Other', label: 'Other' },
]

export const FINDING_SUBCATEGORIES_BY_CATEGORY: Record<string, TaxonomyItem[]> = {
  'Dental Caries': [
    'Small Caries',
    'Medium Caries',
    'Large Caries',
    'Root Caries',
    'Recurrent Caries',
  ].map((s) => ({ value: s, label: s })),
  'Periodontal Disease': [
    'Gingivitis',
    'Mild Periodontitis',
    'Moderate Periodontitis',
    'Severe Periodontitis',
    'Localized Periodontitis',
    'Generalized Periodontitis',
  ].map((s) => ({ value: s, label: s })),
  'Endodontic': [
    'Pulpitis',
    'Necrotic Pulp',
    'Periapical Lesion',
    'Root Canal Needed',
    'Apical Periodontitis',
  ].map((s) => ({ value: s, label: s })),
  'Oral Pathology': [
    'Oral Ulcer',
    'Oral Lesion',
    'Suspicious Lesion',
    'Benign Growth',
    'Inflammatory Condition',
  ].map((s) => ({ value: s, label: s })),
  'Orthodontic': [
    'Crowding',
    'Spacing',
    'Malocclusion',
    'Crossbite',
    'Overbite',
    'Underbite',
  ].map((s) => ({ value: s, label: s })),
  'Oral Surgery': [
    'Impacted Tooth',
    'Extraction Needed',
    'Surgical Exposure',
    'Cyst',
    'Abscess',
  ].map((s) => ({ value: s, label: s })),
  'Prosthodontic': [
    'Missing Tooth',
    'Failed Restoration',
    'Crown Needed',
    'Bridge Needed',
    'Denture Issue',
  ].map((s) => ({ value: s, label: s })),
  'Preventive': [
    'Plaque Buildup',
    'Calculus',
    'Staining',
    'Fluoride Deficiency',
    'Oral Hygiene Issue',
  ].map((s) => ({ value: s, label: s })),
  'TMJ Disorders': [
    'TMJ Pain',
    'Clicking',
    'Limited Opening',
    'Muscle Tension',
    'Joint Dysfunction',
  ].map((s) => ({ value: s, label: s })),
  'Trauma': [
    'Fractured Tooth',
    'Luxated Tooth',
    'Avulsed Tooth',
    'Soft Tissue Injury',
    'Jaw Injury',
  ].map((s) => ({ value: s, label: s })),
  'Congenital Anomalies': [
    'Missing Tooth',
    'Extra Tooth',
    'Malformed Tooth',
    'Enamel Defect',
    'Developmental Anomaly',
  ].map((s) => ({ value: s, label: s })),
  Other: [
    'Unspecified Finding',
    'Multiple Conditions',
    'Complex Case',
    'Requires Further Evaluation',
  ].map((s) => ({ value: s, label: s })),
}

export const FINDING_GROUPS: TaxonomyGroup[] = FINDING_CATEGORIES.map((cat) => ({
  label: cat.label,
  items: FINDING_SUBCATEGORIES_BY_CATEGORY[cat.label] ?? [],
}))

// Treatment procedures
// Label MUST include code and hyphens as requested, and we will save the label as-is
function withCode(code: string, name: string): TaxonomyItem {
  // Using an em dash for readability between code and name
  const label = `${code} — ${name}`
  return { value: label, label }
}

export const TREATMENT_PROCEDURES_BY_CATEGORY: Record<string, TaxonomyItem[]> = {
  Caries: [
    ['D2140', 'Amalgam Filling - 1 Surface'],
    ['D2150', 'Amalgam Filling - 2 Surfaces'],
    ['D2160', 'Amalgam Filling - 3 Surfaces'],
    ['D2330', 'Composite Filling - 1 Surface'],
    ['D2331', 'Composite Filling - 2 Surfaces'],
    ['D2332', 'Composite Filling - 3 Surfaces'],
    ['D2740', 'Crown - Porcelain/Ceramic'],
    ['D2750', 'Crown - Porcelain Fused to Metal'],
  ].map(([c, n]) => withCode(c, n)),
  Periodontal: [
    ['D4210', 'Gingivectomy - Per Quadrant'],
    ['D4240', 'Gingival Flap Procedure'],
    ['D4260', 'Osseous Surgery - Per Quadrant'],
    ['D4341', 'Scaling and Root Planing - Per Quadrant'],
    ['D4355', 'Full Mouth Debridement'],
  ].map(([c, n]) => withCode(c, n)),
  Endodontic: [
    ['D3310', 'Root Canal - Anterior'],
    ['D3320', 'Root Canal - Bicuspid'],
    ['D3330', 'Root Canal - Molar'],
    ['D3410', 'Apicoectomy - Anterior'],
    ['D3421', 'Apicoectomy - Bicuspid'],
    ['D3425', 'Apicoectomy - Molar'],
  ].map(([c, n]) => withCode(c, n)),
  'Oral Surgery': [
    ['D7140', 'Simple Extraction'],
    ['D7210', 'Surgical Extraction'],
    ['D7220', 'Removal of Impacted Tooth - Soft Tissue'],
    ['D7230', 'Removal of Impacted Tooth - Partial Bony'],
    ['D7240', 'Removal of Impacted Tooth - Complete Bony'],
  ].map(([c, n]) => withCode(c, n)),
  Prosthodontic: [
    ['D2740', 'Crown - Porcelain/Ceramic'],
    ['D2750', 'Crown - Porcelain Fused to Metal'],
    ['D6240', 'Pontic - Porcelain Fused to Metal'],
    ['D6750', 'Crown - Porcelain Fused to Metal (Bridge)'],
    ['D5110', 'Complete Denture - Upper'],
    ['D5120', 'Complete Denture - Lower'],
    ['D5213', 'Partial Denture - Upper'],
    ['D5214', 'Partial Denture - Lower'],
  ].map(([c, n]) => withCode(c, n)),
  Preventive: [
    ['D1110', 'Prophylaxis - Adult'],
    ['D1120', 'Prophylaxis - Child'],
    ['D1206', 'Fluoride Varnish'],
    ['D1208', 'Topical Fluoride'],
    ['D1351', 'Sealant - Per Tooth'],
  ].map(([c, n]) => withCode(c, n)),
  Orthodontic: [
    ['D8080', 'Comprehensive Orthodontic Treatment'],
    ['D8090', 'Comprehensive Orthodontic Treatment - Adolescent'],
    ['D8210', 'Removable Appliance Therapy'],
    ['D8220', 'Fixed Appliance Therapy'],
  ].map(([c, n]) => withCode(c, n)),
  'Oral Pathology': [
    ['D7286', 'Biopsy of Oral Tissue - Hard'],
    ['D7287', 'Biopsy of Oral Tissue - Soft'],
    ['D7410', 'Excision of Benign Lesion'],
    ['D7440', 'Excision of Malignant Lesion'],
  ].map(([c, n]) => withCode(c, n)),
  TMJ: [
    ['D7880', 'Occlusal Orthotic Device'],
    ['D7899', 'Unspecified TMJ Therapy'],
    ['D9940', 'Occlusal Guard - Hard Appliance'],
    ['D9944', 'Occlusal Guard - Soft Appliance'],
  ].map(([c, n]) => withCode(c, n)),
  Trauma: [
    ['D7140', 'Simple Extraction'],
    ['D2740', 'Crown - Porcelain/Ceramic'],
    ['D2750', 'Crown - Porcelain Fused to Metal'],
    ['D7210', 'Surgical Extraction'],
    ['D5281', 'Removable Unilateral Partial Denture'],
  ].map(([c, n]) => withCode(c, n)),
  Congenital: [
    ['D7140', 'Simple Extraction'],
    ['D7210', 'Surgical Extraction'],
    ['D8080', 'Comprehensive Orthodontic Treatment'],
    ['D2740', 'Crown - Porcelain/Ceramic'],
  ].map(([c, n]) => withCode(c, n)),
  Other: [
    ['D0150', 'Comprehensive Oral Evaluation'],
    ['D0274', 'Bitewing Radiographs'],
    ['D0330', 'Panoramic Radiograph'],
    ['D9110', 'Palliative Treatment'],
  ].map(([c, n]) => withCode(c, n)),
}

export const COMMON_TREATMENTS: TaxonomyItem[] = [
  ['D1110', 'Prophylaxis - Adult'],
  ['D2330', 'Composite Filling - 1 Surface'],
  ['D2331', 'Composite Filling - 2 Surfaces'],
  ['D2740', 'Crown - Porcelain/Ceramic'],
  ['D3310', 'Root Canal - Anterior'],
  ['D3320', 'Root Canal - Bicuspid'],
  ['D3330', 'Root Canal - Molar'],
  ['D4341', 'Scaling and Root Planing - Per Quadrant'],
  ['D7140', 'Simple Extraction'],
  ['D7210', 'Surgical Extraction'],
].map(([c, n]) => withCode(c as string, n as string))

export const TREATMENT_GROUPS: TaxonomyGroup[] = [
  { label: 'Common', items: COMMON_TREATMENTS },
  ...Object.entries(TREATMENT_PROCEDURES_BY_CATEGORY).map(([label, items]) => ({ label, items })),
]


