import { Invoice, Payment, Treatment, Finding, CaseSheet, Patient, PatientStatus, User } from "@prisma/client";
import prisma from "../prisma";
import type { ExtendedPrismaClient } from "../prisma-extensions";
import { WorkflowError, WorkflowErrorHandler } from "./errors";
import { logger } from "./logger";
export { WorkflowError } from "./errors";

// Types aligned with design.md (simplified for initial structure)
export type WorkflowStep =
  | "PATIENT_CREATED"
  | "USER_CREATED"
  | "CASE_SHEET_CREATED"
  | "TEETH_CREATED"
  | "FINDINGS_DOCUMENTED"
  | "TREATMENTS_PLANNED"
  | "INVOICES_GENERATED"
  | "PAYMENTS_PROCESSED";

export interface WorkflowState {
  patientId: number;
  currentStep: WorkflowStep;
  completedSteps: string[];
  lastUpdated: Date;
  errors?: string[];
}

export interface WorkflowResult {
  patient?: Patient;
  caseSheet?: CaseSheet;
  teethCount?: number;
  finding?: Finding;
  treatment?: Treatment;
  invoice?: Invoice;
  payment?: Payment;
  state?: WorkflowState;
}

export interface PatientCreationData {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  tenantId: string;
  email?: string;
  address?: string;
  dateOfBirth?: Date;
}

export interface TreatmentData {
  findingId: number;
  procedureName: string;
  cost: number | string;
}

export interface FindingData {
  toothId: number;
  description: string;
  // Optional audit field for who recorded the finding
  recordedById?: number;
  severity?: "low" | "medium" | "high";
  // Optional inline treatment creation
  procedureName?: string;
  cost?: number | string;
  // Optional audit field for who created the treatment
  treatmentCreatedById?: number;
}

export interface PaymentData {
  patientId: number;
  invoiceId?: number | null;
  amount: number | string;
  paymentMethod: "CASH" | "CARD" | "CHECK" | "BANK_TRANSFER" | "OTHER";
}

export class ClinicalWorkflowService {
  // Create patient, user (patient), case sheet, and all teeth in a single transaction
  async createPatientWithCompleteWorkflow(data: PatientCreationData): Promise<WorkflowResult> {
    const errors: string[] = [];

    if (!data.firstName?.trim() || !data.lastName?.trim()) {
      errors.push("First name and last name are required");
    }
    if (!data.phoneNumber?.trim()) {
      errors.push("Phone number is required");
    }
    if (!data.tenantId?.trim()) {
      errors.push("Tenant ID is required");
    }
    if (errors.length > 0) {
      throw new WorkflowError("Validation failed", { step: "PATIENT_CREATED", rollbackRequired: false, userMessage: errors.join(", ") });
    }

    logger.info("createPatientWithCompleteWorkflow:start", {
      tenantId: data.tenantId,
      phoneNumber: data.phoneNumber,
    });

    try {
      const result = await prisma.$transaction(async (tx) => {
        // Validate tenant exists to avoid FK violations
        // Ensure multitenancy extension does not inject tenant filter into Tenant queries
        const tenant = await (tx as any).bypassTenant(async () => {
          return (tx as any).tenant.findUnique({ where: { id: data.tenantId } });
        });
        if (!tenant) {
          throw new WorkflowError("Invalid tenant", { step: "PATIENT_CREATED", rollbackRequired: false, userMessage: "Tenant not found or inactive" });
        }
        // Ensure no duplicate patient by tenant + name (per schema unique)
        const existing = await (tx as any).patient.findFirst({
          where: {
            tenantId: data.tenantId,
            firstName: data.firstName.trim(),
            lastName: data.lastName.trim(),
          },
        });
        if (existing) {
          throw new WorkflowError("Duplicate patient", { step: "PATIENT_CREATED", patientId: existing.id, rollbackRequired: false, userMessage: "Patient already exists" });
        }

        // Create patient record
        const patient = await tx.patient.create({
          data: {
            tenantId: data.tenantId,
            firstName: data.firstName.trim(),
            lastName: data.lastName.trim(),
            phoneNumber: data.phoneNumber.trim(),
            email: data.email?.toLowerCase().trim(),
            address: data.address?.trim(),
            dateOfBirth: data.dateOfBirth,
            status: PatientStatus.ACTIVE,
          },
        });

        // Create linked PATIENT user using phoneNumber as username
        const user: User = await tx.user.create({
          data: {
            tenantId: data.tenantId,
            username: data.phoneNumber.trim(),
            phoneNumber: data.phoneNumber.trim(),
            // Minimal secure default; real passwordless/OTP handled elsewhere
            password: "$2a$12$WdWvGmVwL3u3X2U3cvw1peTq3t7k7xv0b8oF9lR0m9qQw1r9a0d6C", // dummy bcrypt for placeholder
            userType: "PATIENT",
            isActive: true,
            firstName: data.firstName.trim(),
            lastName: data.lastName.trim(),
          },
        });

        // Link patient to user
        const patientWithUser = await tx.patient.update({
          where: { id: patient.id },
          data: { userId: user.id },
        });

        // Create case sheet with all teeth via extension
        const caseSheet = await tx.caseSheet.createCaseSheetWithTeeth(patientWithUser.id, data.tenantId);

        // Count created teeth
        const teethCount = caseSheet.teeth?.length ?? (await tx.tooth.count({ where: { caseSheetId: caseSheet.id } }));

        return { patient: patientWithUser, caseSheet, teethCount } satisfies Pick<WorkflowResult, "patient" | "caseSheet" | "teethCount">;
      });

      const workflowState: WorkflowState = {
        patientId: result.patient!.id,
        currentStep: "TEETH_CREATED",
        completedSteps: ["PATIENT_CREATED", "USER_CREATED", "CASE_SHEET_CREATED", "TEETH_CREATED"],
        lastUpdated: new Date(),
      };

      logger.info("createPatientWithCompleteWorkflow:success", {
        patientId: result.patient!.id,
        caseSheetId: result.caseSheet!.id,
        teethCount: result.teethCount,
      });

      return { ...result, state: workflowState };
    } catch (error) {
      await WorkflowErrorHandler.handleWorkflowError(error, {
        operation: "createPatientWithCompleteWorkflow",
        tenantId: data.tenantId,
        phoneNumber: data.phoneNumber,
      });
      if (error instanceof WorkflowError) {
        throw error;
      }
      throw new WorkflowError("Failed to create patient workflow", { step: "PATIENT_CREATED", rollbackRequired: true, userMessage: "Unable to create patient and initialize chart" });
    }
  }

  async searchPatientAndInitializeWorkflow(_searchTerm: string, _tenantId: string): Promise<WorkflowResult> {
    throw new Error("Not implemented");
  }

  // Individual workflow steps (signatures only for now)
  async createPatientWithUser(_patientData: PatientCreationData): Promise<{ patient: Patient }> {
    throw new Error("Not implemented");
  }

  async createCaseSheetWithTeeth(_patientId: number): Promise<{ caseSheet: CaseSheet; teethCount: number }> {
    const patientId = _patientId;

    if (!Number.isInteger(patientId) || patientId <= 0) {
      throw new WorkflowError("Invalid patient ID", { step: "CASE_SHEET_CREATED", rollbackRequired: false, userMessage: "Patient ID must be a positive integer" });
    }

    logger.info("createCaseSheetWithTeeth:start", { patientId });

    try {
      const result = await prisma.$transaction(async (tx: ExtendedPrismaClient) => {
        // Ensure patient exists
        const patient = await tx.patient.findUnique({ where: { id: patientId } });
        if (!patient) {
          throw new WorkflowError("Patient not found", { step: "CASE_SHEET_CREATED", rollbackRequired: false, userMessage: "Cannot create case sheet: patient does not exist" });
        }

        // Ensure patient doesn't already have a case sheet
        const existingCaseSheet = await tx.caseSheet.findFirst({ where: { tenantId: patient.tenantId, patientId } });
        if (existingCaseSheet) {
          throw new WorkflowError("Case sheet already exists for patient", { step: "CASE_SHEET_CREATED", rollbackRequired: false, userMessage: "Patient already has a case sheet" });
        }

        // Create case sheet with nested teeth via extension (includes teeth)
        const caseSheet = await tx.caseSheet.createCaseSheetWithTeeth(patientId, patient.tenantId);

        const teethCount = caseSheet.teeth?.length ?? (await tx.tooth.count({ where: { caseSheetId: caseSheet.id } }));

        return { caseSheet, teethCount } as const;
      });

      logger.info("createCaseSheetWithTeeth:success", { caseSheetId: result.caseSheet.id, teethCount: result.teethCount });

      return result;
    } catch (error) {
      await WorkflowErrorHandler.handleWorkflowError(error, {
        operation: "createCaseSheetWithTeeth",
        patientId,
      });
      if (error instanceof WorkflowError) {
        throw error;
      }
      throw new WorkflowError("Failed to create case sheet with teeth", { step: "CASE_SHEET_CREATED", rollbackRequired: true, userMessage: "Unable to initialize dental chart for patient" });
    }
  }

  async createFindingWithTreatment(_findingData: FindingData): Promise<{ finding: Finding; treatment?: Treatment }> {
    const errors: string[] = [];
    if (!Number.isInteger(_findingData.toothId) || _findingData.toothId <= 0) {
      errors.push("Valid toothId is required");
    }
    if (!_findingData.description || !_findingData.description.trim()) {
      errors.push("Description is required");
    }

    if (errors.length > 0) {
      throw new WorkflowError("Validation failed", {
        step: "FINDINGS_DOCUMENTED",
        rollbackRequired: false,
        userMessage: errors.join(", "),
      });
    }

    const hasTreatmentData =
      typeof _findingData.procedureName === "string" &&
      _findingData.procedureName.trim().length > 0 &&
      _findingData.cost !== undefined;

    logger.info("createFindingWithTreatment:start", {
      toothId: _findingData.toothId,
      hasTreatmentData,
    });

    try {
      const result = await prisma.$transaction(async (tx: ExtendedPrismaClient) => {
        // Validate tooth existence (tenant inferred downstream by extensions)
        const tooth = await tx.tooth.findUnique({ where: { id: _findingData.toothId } });
        if (!tooth) {
          throw new WorkflowError("Tooth not found", {
            step: "FINDINGS_DOCUMENTED",
            rollbackRequired: false,
            userMessage: "Cannot add finding: tooth does not exist",
          });
        }

        // Create finding directly to avoid extension context issues; infer tenantId from tooth
        const finding = await tx.finding.create({
          data: {
            tenantId: (tooth as any).tenantId as string,
            toothId: _findingData.toothId,
            description: _findingData.description.trim(),
            recordedById: _findingData.recordedById,
            severity: _findingData.severity ? String(_findingData.severity) : undefined,
            // createdById can mirror recordedById if provided
            ...(_findingData.recordedById ? { createdById: _findingData.recordedById } : {}),
          },
        });

        // Optionally create a treatment if provided inline
        let treatment: Treatment | undefined;
        if (hasTreatmentData) {
          const numericCost = typeof _findingData.cost === 'string' || typeof _findingData.cost === 'number'
            ? (_findingData.cost as any)
            : undefined;
          if (numericCost === undefined) {
            throw new WorkflowError("cost must be a number or numeric string", {
              step: "TREATMENTS_PLANNED",
              rollbackRequired: false,
              userMessage: "Invalid cost",
            });
          }
          treatment = await tx.treatment.create({
            data: {
              tenantId: (tooth as any).tenantId as string,
              findingId: finding.id,
              procedureName: (_findingData.procedureName as string).trim(),
              cost: numericCost,
              ...(_findingData.treatmentCreatedById ? { createdById: _findingData.treatmentCreatedById } : {}),
            },
          });
        }

        return { finding, treatment } as const;
      });

      logger.info("createFindingWithTreatment:success", {
        findingId: result.finding.id,
        treatmentId: result.treatment?.id ?? null,
      });

      return result;
    } catch (error) {
      await WorkflowErrorHandler.handleWorkflowError(error, {
        operation: "createFindingWithTreatment",
        toothId: _findingData.toothId,
      });
      if (error instanceof WorkflowError) {
        throw error;
      }
      throw new WorkflowError(`Failed to create finding (and optional treatment): ${(error as Error)?.message ?? ''}`.trim(), {
        step: "FINDINGS_DOCUMENTED",
        rollbackRequired: true,
        userMessage: "Unable to save finding/treatment",
      });
    }
  }

  async createTreatmentWithInvoice(_treatmentData: TreatmentData): Promise<{ treatment: Treatment; invoice: Invoice }> {
    const errors: string[] = [];
    if (!Number.isInteger(_treatmentData.findingId) || _treatmentData.findingId <= 0) {
      errors.push("Valid findingId is required");
    }
    if (!_treatmentData.procedureName || !_treatmentData.procedureName.trim()) {
      errors.push("procedureName is required");
    }
    if (_treatmentData.cost === undefined || _treatmentData.cost === null || `${_treatmentData.cost}`.trim() === "") {
      errors.push("cost is required");
    }

    if (errors.length > 0) {
      throw new WorkflowError("Validation failed", {
        step: "TREATMENTS_PLANNED",
        rollbackRequired: false,
        userMessage: errors.join(", "),
      });
    }

    logger.info("createTreatmentWithInvoice:start", {
      findingId: _treatmentData.findingId,
      procedureName: _treatmentData.procedureName,
    });

    try {
      const result = await prisma.$transaction(async (tx: ExtendedPrismaClient) => {
        // Ensure finding exists and resolve patientId via tooth -> caseSheet
        const finding = await tx.finding.findUnique({
          where: { id: _treatmentData.findingId },
          include: {
            tooth: {
              include: {
                caseSheet: true,
              },
            },
          },
        });

        if (!finding) {
          throw new WorkflowError("Finding not found", {
            step: "TREATMENTS_PLANNED",
            rollbackRequired: false,
            userMessage: "Cannot create treatment: finding does not exist",
          });
        }

        const patientId = finding.tooth?.caseSheet?.patientId;
        if (!patientId) {
          throw new WorkflowError("Related patient not found", {
            step: "TREATMENTS_PLANNED",
            rollbackRequired: false,
            userMessage: "Unable to determine patient for invoice",
          });
        }

        const numericCost = typeof _treatmentData.cost === 'string' || typeof _treatmentData.cost === 'number'
          ? (_treatmentData.cost as any)
          : undefined;
        if (numericCost === undefined) {
          throw new WorkflowError("cost must be a number or numeric string", {
            step: "TREATMENTS_PLANNED",
            rollbackRequired: false,
            userMessage: "Invalid cost",
          });
        }
        const treatment = await tx.treatment.create({
          data: {
            tenantId: finding.tenantId,
            findingId: _treatmentData.findingId,
            procedureName: _treatmentData.procedureName.trim(),
            cost: numericCost,
          },
        });

        // Find an existing invoice that already contains treatments for THIS finding (preferred), still open (DRAFT/SENT)
        let invoice = await tx.invoice.findFirst({
          where: {
            patientId,
            treatments: { some: { findingId: _treatmentData.findingId } },
            OR: [{ status: "DRAFT" as any }, { status: "SENT" as any }],
          },
        });

        // If not found, create a fresh invoice dedicated to this finding using extension
        if (!invoice) {
          invoice = await tx.invoice.createInvoice({
            tenantId: finding.tenantId,
            patientId,
            invoiceDate: new Date(),
            totalAmount: 0 as any,
          } as any);
        }

        // Link ALL uninvoiced treatments under this finding to the chosen invoice, including the newly created one
        const uninvoicedTreatmentsForFinding = await tx.treatment.findMany({
          where: { findingId: _treatmentData.findingId, invoiceId: null },
          select: { id: true, cost: true },
        });

        const idsToLink = [treatment.id, ...uninvoicedTreatmentsForFinding.map((t: { id: number }) => t.id)].filter(
          (v, i, arr) => arr.indexOf(v) === i
        );

        if (idsToLink.length > 0) {
          await tx.treatment.updateMany({ where: { id: { in: idsToLink } }, data: { invoiceId: invoice.id } });
        }

        // Recalculate totals from all treatments currently linked to the invoice
        const allLinked: Array<{ cost: any }> = await tx.treatment.findMany({ where: { invoiceId: invoice.id }, select: { cost: true } });
        let newTotal = 0;
        for (const item of allLinked) {
          newTotal += Number((item as any).cost as any);
        }
        const newBalance = newTotal - Number(invoice.amountPaid as any);
        const updatedInvoice = await tx.invoice.update({
          where: { id: invoice.id },
          data: { totalAmount: newTotal as any, balanceDue: (newBalance < 0 ? 0 : newBalance) as any },
        });

        // Return the newly created treatment linked to the updated invoice
        const treatmentLinked = await tx.treatment.update({
          where: { id: treatment.id },
          data: { invoiceId: updatedInvoice.id },
        });

        return { treatment: treatmentLinked, invoice: updatedInvoice } as const;
      });

      logger.info("createTreatmentWithInvoice:success", {
        treatmentId: result.treatment.id,
        invoiceId: result.invoice.id,
      });

      return result;
    } catch (error) {
      await WorkflowErrorHandler.handleWorkflowError(error, {
        operation: "createTreatmentWithInvoice",
        findingId: _treatmentData.findingId,
      });
      if (error instanceof WorkflowError) {
        throw error;
      }
      throw new WorkflowError("Failed to create treatment and update invoice", {
        step: "TREATMENTS_PLANNED",
        rollbackRequired: true,
        userMessage: "Unable to save treatment and update invoice",
      });
    }
  }

  async processPaymentWithUpdate(_paymentData: PaymentData): Promise<{ payment: Payment; invoice?: Invoice }> {
    const errors: string[] = [];
    if (!Number.isInteger(_paymentData.patientId) || _paymentData.patientId <= 0) {
      errors.push("Valid patientId is required");
    }
    if (_paymentData.amount === undefined || _paymentData.amount === null || `${_paymentData.amount}`.trim() === "") {
      errors.push("amount is required");
    }
    if (!_paymentData.paymentMethod) {
      errors.push("paymentMethod is required");
    }

    if (errors.length > 0) {
      throw new WorkflowError("Validation failed", {
        step: "PAYMENTS_PROCESSED",
        rollbackRequired: false,
        userMessage: errors.join(", "),
      });
    }

    logger.info("processPaymentWithUpdate:start", {
      patientId: _paymentData.patientId,
      invoiceId: _paymentData.invoiceId ?? null,
      method: _paymentData.paymentMethod,
    });

    try {
      const result = await prisma.$transaction(async (tx: ExtendedPrismaClient) => {
        // Resolve target invoice if not provided: pick most recent with balanceDue > 0
        let targetInvoiceId: number | null | undefined = _paymentData.invoiceId ?? undefined;

        if (!targetInvoiceId) {
          const candidate = await tx.invoice.findFirst({
            where: {
              patientId: _paymentData.patientId,
              balanceDue: { gt: 0 as any },
            },
            orderBy: [{ invoiceDate: "desc" }],
          });
          targetInvoiceId = candidate?.id;
        }

        // If invoice provided or found, ensure it exists
        let preInvoice: Invoice | null = null;
        if (targetInvoiceId) {
          preInvoice = await tx.invoice.findUnique({ where: { id: targetInvoiceId } });
          if (!preInvoice) {
            throw new WorkflowError("Invoice not found", {
              step: "PAYMENTS_PROCESSED",
              rollbackRequired: false,
              userMessage: "Specified invoice does not exist",
            });
          }
        }

        // Create payment via extension (infers tenant and updates aggregates)
        const patient = await tx.patient.findUnique({ where: { id: _paymentData.patientId } });
        if (!patient) {
          throw new WorkflowError("Patient not found", {
            step: "PAYMENTS_PROCESSED",
            rollbackRequired: false,
            userMessage: "Patient does not exist",
          });
        }
        const numericAmount = typeof _paymentData.amount === 'string' ? Number(_paymentData.amount) : (_paymentData.amount as number);
        if (!Number.isFinite(numericAmount)) {
          throw new WorkflowError("amount must be a finite number", {
            step: "PAYMENTS_PROCESSED",
            rollbackRequired: false,
            userMessage: "Invalid amount",
          });
        }
        const payment = await (tx as any).payment.processPayment(
          _paymentData.patientId,
          preInvoice ? preInvoice.id : null,
          numericAmount,
          _paymentData.paymentMethod as any,
        );

        // Fetch updated invoice if linked
        let updatedInvoice: Invoice | undefined = undefined;
        if ((payment as any).invoiceId) {
          const inv = await tx.invoice.findUnique({ where: { id: (payment as any).invoiceId } });
          if (inv) {
            // Recalculate aggregates from completed payments
            const payments = await tx.payment.findMany({ where: { invoiceId: inv.id, status: "COMPLETED" as any } });
            const totalPaidNumber = payments.reduce((sum: number, p: { amount: any }) => sum + Number(p.amount), 0);
            const newBalanceNumber = Number(inv.totalAmount) - totalPaidNumber;
            let newStatus = inv.status;
            if (totalPaidNumber >= Number(inv.totalAmount)) {
              newStatus = "PAID" as any;
            } else if (totalPaidNumber > 0) {
              newStatus = "PARTIALLY_PAID" as any;
            } else {
              // No payments and balance remaining
              newStatus = "SENT" as any;
            }
            if (newBalanceNumber < 0) {
              // Clamp overpayment: set balance to 0 and amountPaid to totalAmount
              updatedInvoice = await tx.invoice.update({
                where: { id: inv.id },
                data: {
                  amountPaid: inv.totalAmount as any,
                  balanceDue: 0 as any,
                  status: "PAID" as any,
                },
              });
            } else {
              updatedInvoice = await tx.invoice.update({
                where: { id: inv.id },
                data: {
                  amountPaid: totalPaidNumber as any,
                  balanceDue: newBalanceNumber as any,
                  status: newStatus,
                },
              });
            }
          }
        }

        return { payment, invoice: updatedInvoice } as const;
      });

      logger.info("processPaymentWithUpdate:success", {
        paymentId: result.payment.id,
        invoiceId: result.invoice?.id ?? null,
      });

      return result;
    } catch (error) {
      await WorkflowErrorHandler.handleWorkflowError(error, {
        operation: "processPaymentWithUpdate",
        patientId: _paymentData.patientId,
        invoiceId: _paymentData.invoiceId ?? null,
      });
      if (error instanceof WorkflowError) {
        throw error;
      }
      throw new WorkflowError("Failed to process payment and update invoice", {
        step: "PAYMENTS_PROCESSED",
        rollbackRequired: true,
        userMessage: "Unable to record payment",
      });
    }
  }
}
