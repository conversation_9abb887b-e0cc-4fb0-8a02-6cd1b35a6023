export class WorkflowError extends Error {
  step: string
  patientId?: number
  rollbackRequired: boolean
  userMessage: string

  constructor(
    message: string,
    options: { step: string; patientId?: number; rollbackRequired?: boolean; userMessage?: string }
  ) {
    super(message)
    this.name = 'WorkflowError'
    this.step = options.step
    this.patientId = options.patientId
    this.rollbackRequired = options.rollbackRequired ?? false
    this.userMessage = options.userMessage ?? message
  }
}

export function toUserMessage(error: unknown): string {
  if (error instanceof WorkflowError) return error.userMessage
  const message = (error as Error)?.message
  if (typeof message === 'string' && message.trim().length > 0) return message
  return 'An unexpected error occurred. Please try again.'
}

export class WorkflowErrorHandler {
  static createUserFriendlyMessage(error: unknown): string {
    return toUserMessage(error)
  }

  static async handleWorkflowError(error: unknown, context?: Record<string, unknown>): Promise<void> {
    try {
      const { logger } = await import('./logger')
      const { trackError } = await import('./monitoring')
      const payload = {
        name: error instanceof Error ? error.name : 'UnknownError',
        message: (error as any)?.message ?? String(error),
        step: error instanceof WorkflowError ? error.step : undefined,
        rollbackRequired: error instanceof WorkflowError ? error.rollbackRequired : undefined,
        context: context ?? {},
      }
      logger.error('Workflow error', payload)
      trackError('WorkflowError', payload)
    } catch {
      // no-op: avoid secondary failures during error handling
    }
  }

  // Placeholder hook for future transactional rollbacks if needed beyond Prisma.$transaction
  static async rollbackWorkflow(_patientId: number, _fromStep: string): Promise<void> {
    return
  }
}
