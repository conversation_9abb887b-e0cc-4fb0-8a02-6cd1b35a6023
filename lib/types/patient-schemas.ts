import { z } from "zod"

export const patientFormSchema = z.object({
  firstName: z.string().min(1, "First name is required").max(50, "First name must be less than 50 characters").trim(),
  lastName: z.string().min(1, "Last name is required").max(50, "Last name must be less than 50 characters").trim(),
  phoneNumber: z
    .string()
    .min(7, "Phone number is too short")
    .max(20, "Phone number is too long")
    .regex(/^\+?[\d\s-()]+$/, "Invalid phone number"),
  email: z
    .string()
    .email("Invalid email")
    .max(255, "Email must be less than 255 characters")
    .optional()
    .or(z.literal("")),
  address: z
    .string()
    .max(255, "Address must be less than 255 characters")
    .optional()
    .or(z.literal("")),
  dateOfBirth: z
    .string()
    .optional()
    .or(z.literal("")),
})

export type PatientFormData = z.infer<typeof patientFormSchema>
