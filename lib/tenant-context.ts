/**
 * Tenant Context Management System
 * 
 * This module provides tenant context management for multitenancy.
 * In a real application, this would integrate with your authentication
 * and session management system.
 */

import { AsyncLocalStorage } from 'async_hooks';
import { UserType } from './auth/types';

// Type definitions
export interface TenantContextData {
  tenantId: string;
  tenantName?: string;
  userId?: string;
  userRole?: UserType;
  sessionId?: string;
  isAuthenticated?: boolean;
}

// AsyncLocalStorage for request-scoped tenant context
const tenantStorage = new AsyncLocalStorage<TenantContextData>();

// Global fallback for simple scenarios (not recommended for production)
let globalTenantContext: TenantContextData | null = null;

export class TenantContextManager {
  /**
   * Get current tenant context from AsyncLocalStorage or global fallback
   */
  static getCurrentContext(): TenantContextData | null {
    // Try to get from AsyncLocalStorage first (request-scoped)
    const asyncContext = tenantStorage.getStore();
    if (asyncContext) {
      console.log(`[TENANT-DEBUG] Retrieved async context: ${JSON.stringify(asyncContext)}`);
      return asyncContext;
    }

    // Fallback to global context
    console.log(`[TENANT-DEBUG] Retrieved global context: ${globalTenantContext ? JSON.stringify(globalTenantContext) : 'null'}`);
    return globalTenantContext;
  }

  /**
   * Get current tenant ID
   */
  static getCurrentTenantId(): string | null {
    const context = this.getCurrentContext();
    const tenantId = context?.tenantId || null;
    console.log(`[TENANT-DEBUG] getCurrentTenantId() returning: ${tenantId}`);
    return tenantId;
  }

  /**
   * Set tenant context globally (for simple scenarios)
   */
  static setGlobalContext(context: TenantContextData): void {
    console.log(`[TENANT-DEBUG] Setting global context: ${JSON.stringify(context)}`);
    globalTenantContext = context;
  }

  /**
   * Clear global tenant context
   */
  static clearGlobalContext(): void {
    console.log(`[TENANT-DEBUG] Clearing global context`);
    globalTenantContext = null;
  }

  /**
   * Run a function with specific tenant context (request-scoped)
   * This is the recommended approach for production applications
   */
  static async runWithContext<T>(
    context: TenantContextData,
    fn: () => Promise<T>
  ): Promise<T> {
    return tenantStorage.run(context, fn);
  }

  /**
   * Run a function with specific tenant context (synchronous)
   */
  static runWithContextSync<T>(
    context: TenantContextData,
    fn: () => T
  ): T {
    return tenantStorage.run(context, fn);
  }

  /**
   * Create tenant context from session data
   * This integrates with the authentication system
   */
  static createContextFromSession(sessionData: {
    tenantId: string;
    tenantName?: string;
    userId?: string;
    userRole?: UserType;
    sessionId?: string;
  }): TenantContextData {
    return {
      tenantId: sessionData.tenantId,
      tenantName: sessionData.tenantName,
      userId: sessionData.userId,
      userRole: sessionData.userRole,
      sessionId: sessionData.sessionId,
      isAuthenticated: true,
    };
  }

  /**
   * Create tenant context from authentication session
   * This integrates with our AuthService SessionInfo
   */
  static createContextFromAuthSession(sessionInfo: {
    userId: string;
    tenantId: string;
    username: string;
    userType: UserType;
    isActive: boolean;
  }, sessionId?: string, tenantName?: string): TenantContextData {
    return {
      tenantId: sessionInfo.tenantId,
      tenantName: tenantName,
      userId: sessionInfo.userId,
      userRole: sessionInfo.userType,
      sessionId: sessionId,
      isAuthenticated: sessionInfo.isActive,
    };
  }

  /**
   * Create tenant context from JWT token
   * This would typically decode and validate a JWT token
   */
  static createContextFromJWT(token: string): TenantContextData {
    // In a real implementation, you would decode and validate the JWT
    // For now, this is a placeholder
    try {
      // Placeholder JWT decoding logic
      const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
      return {
        tenantId: payload.tenantId,
        tenantName: payload.tenantName,
        userId: payload.userId,
        userRole: payload.role,
      };
    } catch (error) {
      throw new Error('Invalid JWT token');
    }
  }

  /**
   * Middleware helper for Next.js API routes
   */
  static createNextJsMiddleware() {
    return async (req: any, res: any, next: () => Promise<void>) => {
      // Extract tenant information from request
      // This could come from headers, query params, subdomain, etc.
      const tenantId = req.headers['x-tenant-id'] || req.query.tenantId;
      const userId = req.headers['x-user-id'] || req.user?.id;
      const userRole = req.headers['x-user-role'] || req.user?.role;

      if (tenantId) {
        const context: TenantContextData = {
          tenantId: tenantId as string,
          userId: userId as string,
          userRole: userRole as string,
        };

        // Run the request handler with tenant context
        return this.runWithContext(context, next);
      }

      // No tenant context available, proceed without it
      return next();
    };
  }

  /**
   * Express.js middleware
   */
  static createExpressMiddleware() {
    return (req: any, res: any, next: any) => {
      const tenantId = req.headers['x-tenant-id'] || req.query.tenantId;
      const userId = req.headers['x-user-id'] || req.user?.id;
      const userRole = req.headers['x-user-role'] || req.user?.role;

      if (tenantId) {
        const context: TenantContextData = {
          tenantId: tenantId as string,
          userId: userId as string,
          userRole: userRole as string,
        };

        // Run the request handler with tenant context
        this.runWithContextSync(context, () => next());
      } else {
        next();
      }
    };
  }

  /**
   * Validate tenant access for a user
   * This would typically check against your authorization system
   */
  static async validateTenantAccess(
    userId: string,
    tenantId: string
  ): Promise<boolean> {
    // Placeholder validation logic
    // In a real implementation, you would check:
    // 1. User exists
    // 2. User has access to the tenant
    // 3. User's permissions within the tenant
    
    // For now, always return true
    return true;
  }

  /**
   * Get tenant information
   */
  static async getTenantInfo(tenantId: string): Promise<{
    id: string;
    name: string;
    status: string;
  } | null> {
    // Placeholder - in real implementation, fetch from database
    return {
      id: tenantId,
      name: `Tenant ${tenantId}`,
      status: 'active',
    };
  }
}

// Export for backward compatibility with the multitenancy extension
export const TenantContext = {
  getCurrentTenantId: () => TenantContextManager.getCurrentTenantId(),
  setCurrentTenantId: (tenantId: string) => 
    TenantContextManager.setGlobalContext({ tenantId }),
  clearTenantId: () => TenantContextManager.clearGlobalContext(),
};