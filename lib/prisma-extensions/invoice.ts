import { Prisma, InvoiceStatus } from "@prisma/client";
import { generateInvoiceSerial } from "../serials";

interface CreateInvoiceInput {
  patientId: number;
  invoiceDate: Date;
  totalAmount?: number;
  tenantId: string;
  createdById?: number;
}

export const invoiceExtension = Prisma.defineExtension({
  name: "invoiceExtension",
  model: {
    invoice: {
      // Create invoice with auto-generated tenant-scoped serial
      async createInvoice(data: CreateInvoiceInput) {
        const prismaLike = Prisma.getExtensionContext(this) as any;
        const run = async (ctx: any) => {
          const serial = await generateInvoiceSerial(data.tenantId, ctx);
          const totalAmount = data.totalAmount || 0;
          return ctx.invoice.create({
            data: {
              ...data,
              serial,
              totalAmount,
              balanceDue: totalAmount,
              status: InvoiceStatus.DRAFT,
            },
          });
        }
        if (typeof prismaLike.$transaction === 'function') {
          return prismaLike.$transaction(async (trx: any) => run(trx));
        }
        return run(prismaLike);
      },

      // Update invoice amount and recalculate balance
      async updateInvoiceAmount(
        invoiceId: number,
        totalAmount: number,
        updatedById?: number
      ) {
        const invoice = await this.findUnique({
          where: { id: invoiceId },
        });

        if (!invoice) {
          throw new Error('Invoice not found');
        }

        const newBalanceDue = totalAmount - invoice.amountPaid;

        return this.update({
          where: { id: invoiceId },
          data: {
            totalAmount,
            balanceDue: newBalanceDue,
            updatedById,
          },
        });
      },

      // Update amount paid and recalculate balance
      async updateAmountPaid(
        invoiceId: number,
        amountPaid: number,
        updatedById?: number
      ) {
        const invoice = await this.findUnique({
          where: { id: invoiceId },
        });

        if (!invoice) {
          throw new Error('Invoice not found');
        }

        const newBalanceDue = invoice.totalAmount - amountPaid;
        let newStatus = invoice.status;

        // Update status based on payment
        if (amountPaid >= invoice.totalAmount) {
          newStatus = InvoiceStatus.PAID;
        }

        return this.update({
          where: { id: invoiceId },
          data: {
            amountPaid,
            balanceDue: newBalanceDue,
            status: newStatus,
            updatedById,
          },
        });
      },

      // Update invoice status with basic validation
      async updateInvoiceStatus(
        invoiceId: number,
        newStatus: InvoiceStatus,
        updatedById?: number
      ) {
        const invoice = await this.findUnique({
          where: { id: invoiceId },
        });

        if (!invoice) {
          throw new Error('Invoice not found');
        }

        // Basic status validation - prevent invalid transitions
        if (invoice.status === InvoiceStatus.PAID && newStatus !== InvoiceStatus.PAID) {
          throw new Error('Cannot change status of a paid invoice');
        }

        return this.update({
          where: { id: invoiceId },
          data: {
            status: newStatus,
            updatedById,
          },
        });
      },

      // Get invoices by status
      async getInvoicesByStatus(tenantId: string, status: InvoiceStatus) {
        return this.findMany({
          where: {
            tenantId,
            status,
          },
          include: {
            patient: true,
          },
          orderBy: {
            invoiceDate: 'desc',
          },
        });
      },

      // Get overdue invoices
      async getOverdueInvoices(tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            status: InvoiceStatus.OVERDUE,
            balanceDue: {
              gt: 0,
            },
          },
          include: {
            patient: true,
          },
          orderBy: {
            invoiceDate: 'desc',
          },
        });
      },

      // Get invoices for a patient
      async getInvoicesForPatient(tenantId: string, patientId: number) {
        return this.findMany({
          where: {
            tenantId,
            patientId,
          },
          include: {
            payments: true,
          },
          orderBy: {
            invoiceDate: 'desc',
          },
        });
      },

      // Mark invoice as sent
      async markInvoiceAsSent(
        invoiceId: number,
        updatedById?: number
      ) {
        return this.update({
          where: { id: invoiceId },
          data: {
            status: InvoiceStatus.SENT,
            updatedById,
          },
        });
      },

      // Validate invoice data
      async validateInvoice(data: Partial<CreateInvoiceInput & { id?: number }>) {
        const errors: string[] = [];

        // Validate required fields
        if (!data.patientId) {
          errors.push('Patient is required');
        }

        if (!data.invoiceDate) {
          errors.push('Invoice date is required');
        }

        if (!data.tenantId) {
          errors.push('Tenant ID is required');
        }

        // Validate amounts
        if (data.totalAmount !== undefined && data.totalAmount < 0) {
          errors.push('Total amount cannot be negative');
        }

        return {
          isValid: errors.length === 0,
          errors,
        };
      },



      // Get basic invoice summary statistics
      async getInvoiceSummary(tenantId: string) {
        const invoices = await this.findMany({
          where: { tenantId },
        });

        const summary = {
          totalInvoices: invoices.length,
          totalAmount: 0,
          totalPaid: 0,
          totalOutstanding: 0,
          statusBreakdown: {} as Record<InvoiceStatus, { count: number; amount: number }>,
        };

        // Initialize status breakdown
        Object.values(InvoiceStatus).forEach(status => {
          summary.statusBreakdown[status] = { count: 0, amount: 0 };
        });

        invoices.forEach(invoice => {
          const totalAmount = Number(invoice.totalAmount);
          const amountPaid = Number(invoice.amountPaid);
          const balanceDue = Number(invoice.balanceDue);

          summary.totalAmount += totalAmount;
          summary.totalPaid += amountPaid;
          summary.totalOutstanding += balanceDue;

          summary.statusBreakdown[invoice.status].count++;
          summary.statusBreakdown[invoice.status].amount += totalAmount;
        });

        return summary;
      },

      // Create or update invoice for a patient by adding a treatment cost
      async createOrUpdateForPatient(patientId: number, treatmentCost: any, createdById?: number) {
        const prisma = Prisma.getExtensionContext(this) as any;

        // Fetch patient to infer tenant
        const patient = await prisma.patient.findUnique({ where: { id: patientId } });
        if (!patient) {
          throw new Error("Patient not found");
        }

        const numericCost = typeof treatmentCost === 'string' || typeof treatmentCost === 'number'
          ? treatmentCost
          : undefined;
        if (numericCost === undefined) {
          throw new Error("treatmentCost must be a number or numeric string");
        }

        // Try to find existing draft or sent invoice for patient
        let invoice = await this.findFirst({
          where: {
            tenantId: patient.tenantId,
            patientId,
            OR: [
              { status: InvoiceStatus.DRAFT },
              { status: InvoiceStatus.SENT },
            ],
          },
        });

        if (!invoice) {
          // Create a new invoice with tenant-scoped serial
          const serial = await generateInvoiceSerial(patient.tenantId, prisma);
          invoice = await this.create({
            data: {
              tenantId: patient.tenantId,
              patientId,
              serial,
              invoiceDate: new Date(),
              status: InvoiceStatus.DRAFT,
              totalAmount: numericCost as any,
              amountPaid: 0 as any,
              balanceDue: numericCost as any,
              ...(createdById ? { createdById } : {}),
            },
          });
        } else {
          // Update existing invoice totals
          const newTotal = (invoice.totalAmount as any) + (numericCost as any);
          const newBalance = (newTotal as any) - (invoice.amountPaid as any);
          invoice = await this.update({
            where: { id: invoice.id },
            data: {
              totalAmount: newTotal as any,
              balanceDue: newBalance as any,
              ...(createdById ? { updatedById: createdById } : {}),
            },
          });
        }

        return invoice;
      },

      // Add treatment cost to an existing invoice
      async addTreatmentCost(invoiceId: number, cost: any, updatedById?: number) {
        const invoice = await this.findUnique({ where: { id: invoiceId } });
        if (!invoice) {
          throw new Error("Invoice not found");
        }

        const numericCost = typeof cost === 'string' || typeof cost === 'number' ? cost : undefined;
        if (numericCost === undefined) {
          throw new Error("cost must be a number or numeric string");
        }

        const newTotal = (invoice.totalAmount as any) + (numericCost as any);
        const newBalance = (newTotal as any) - (invoice.amountPaid as any);

        return this.update({
          where: { id: invoiceId },
          data: {
            totalAmount: newTotal as any,
            balanceDue: newBalance as any,
            ...(updatedById ? { updatedById } : {}),
          },
        });
      },

      // Update amounts directly (e.g., after manual adjustments)
      async updateAmounts(invoiceId: number, totalAmount: any, updatedById?: number) {
        const invoice = await this.findUnique({ where: { id: invoiceId } });
        if (!invoice) {
          throw new Error("Invoice not found");
        }

        const numericTotal = typeof totalAmount === 'string' || typeof totalAmount === 'number'
          ? totalAmount
          : undefined;
        if (numericTotal === undefined) {
          throw new Error("totalAmount must be a number or numeric string");
        }

        const newBalance = (numericTotal as any) - (invoice.amountPaid as any);
        return this.update({
          where: { id: invoiceId },
          data: {
            totalAmount: numericTotal as any,
            balanceDue: newBalance as any,
            ...(updatedById ? { updatedById } : {}),
          },
        });
      },

      // Find invoice with its payments
      async findWithPayments(invoiceId: number) {
        return this.findUnique({
          where: { id: invoiceId },
          include: {
            payments: true,
          },
        });
      },
    },
  },
  result: {
    invoice: {
      // Check if invoice is overdue
      isOverdue: {
        needs: { status: true },
        compute(invoice) {
          return invoice.status === InvoiceStatus.OVERDUE;
        },
      },

      // Check if invoice is paid in full
      isPaidInFull: {
        needs: { balanceDue: true },
        compute(invoice) {
          return Number(invoice.balanceDue) <= 0;
        },
      },

      // Get payment percentage
      paymentPercentage: {
        needs: { totalAmount: true, amountPaid: true },
        compute(invoice) {
          const totalAmount = Number(invoice.totalAmount);
          const amountPaid = Number(invoice.amountPaid);
          
          if (totalAmount === 0) return 0;
          return Math.round((amountPaid / totalAmount) * 100);
        },
      },

      // Get status color for UI
      statusColor: {
        needs: { status: true },
        compute(invoice) {
          switch (invoice.status) {
            case InvoiceStatus.DRAFT:
              return 'gray';
            case InvoiceStatus.SENT:
              return 'blue';
            case InvoiceStatus.PAID:
              return 'green';
            case InvoiceStatus.OVERDUE:
              return 'red';
            default:
              return 'gray';
          }
        },
      },

      // Format invoice serial for display
      displayInvoiceNumber: {
        needs: { serial: true },
        compute(invoice) {
          return `#${invoice.serial}`;
        },
      },

      // Check if invoice can be edited
      canBeEdited: {
        needs: { status: true },
        compute(invoice) {
          return invoice.status === InvoiceStatus.DRAFT;
        },
      },
    },
  },
});