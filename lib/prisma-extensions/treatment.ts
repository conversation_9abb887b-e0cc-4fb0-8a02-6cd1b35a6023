import { Prisma, TreatmentStatus } from "@prisma/client";

// Common procedures for basic treatment tracking
const COMMON_PROCEDURES = [
  { name: 'Prophylaxis - adult', averageCost: 100 },
  { name: 'Comprehensive oral evaluation', averageCost: 100 },
  { name: 'Periodic oral evaluation', averageCost: 80 },
  { name: 'Amalgam filling', averageCost: 150 },
  { name: 'Composite filling', averageCost: 200 },
  { name: 'Tooth extraction', averageCost: 150 },
  { name: 'Crown', averageCost: 1200 },
  { name: 'Root canal therapy', averageCost: 800 },
  { name: 'Scaling and root planing', averageCost: 300 },
  { name: 'Fluoride treatment', averageCost: 50 },
];

export const treatmentExtension = Prisma.defineExtension({
  name: "treatmentExtension",
  model: {
    treatment: {
      // Create treatment and infer tenantId from the finding
      async createTreatment(findingId: number, procedureName: string, cost: any, createdById?: number) {
        if (!procedureName || !procedureName.trim()) {
          throw new Error("procedureName is required");
        }
        // Cost validation: allow numeric or Decimal-like string
        const numericCost = typeof cost === 'string' || typeof cost === 'number' ? cost : undefined;
        if (numericCost === undefined) {
          throw new Error("cost must be a number or numeric string");
        }

        const prisma = Prisma.getExtensionContext(this) as any;
        const finding = await prisma.finding.findUnique({ where: { id: findingId } });
        if (!finding) {
          throw new Error("Finding not found");
        }

        return this.create({
          data: {
            tenantId: finding.tenantId,
            findingId,
            procedureName: procedureName.trim(),
            cost: numericCost as any,
            ...(createdById ? { createdById } : {}),
          },
        });
      },

      // Update treatment status
      async updateStatus(treatmentId: number, status: TreatmentStatus, updatedById?: number) {
        const existing = await this.findUnique({ where: { id: treatmentId } });
        if (!existing) {
          throw new Error("Treatment not found");
        }

        return this.update({
          where: { id: treatmentId },
          data: {
            status,
            ...(updatedById ? { updatedById } : {}),
          },
        });
      },

      // Mark as completed with date and optional completedById
      async markCompleted(treatmentId: number, completedById?: number) {
        const existing = await this.findUnique({ where: { id: treatmentId } });
        if (!existing) {
          throw new Error("Treatment not found");
        }

        return this.update({
          where: { id: treatmentId },
          data: {
            status: TreatmentStatus.COMPLETED,
            completedDate: new Date(),
            ...(completedById ? { completedById } : {}),
          },
        });
      },

      // Get common procedures for treatment creation
      getCommonProcedures() {
        return COMMON_PROCEDURES;
      },

      // Get treatments for a patient
      async getTreatmentsForPatient(tenantId: string, patientId: number) {
        return this.findMany({
          where: {
            tenantId,
            finding: {
              tooth: {
                caseSheet: {
                  patientId,
                },
              },
            },
          },
          include: {
            finding: {
              include: {
                tooth: true,
              },
            },
            completedBy: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
        });
      },

      // Get treatments for a specific finding
      async getTreatmentsForFinding(tenantId: string, findingId: number) {
        return this.findMany({
          where: {
            tenantId,
            findingId,
          },
          include: {
            finding: true,
            completedBy: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
        });
      },

      // Update treatment cost
      async updateCost(treatmentId: number, cost: number, updatedById?: number) {
        if (cost < 0) {
          throw new Error('Treatment cost cannot be negative');
        }

        return this.update({
          where: { id: treatmentId },
          data: {
            cost,
            updatedById,
            updatedAt: new Date(),
          },
        });
      },
    },
  },
  result: {
    treatment: {
      // Check if treatment is completed
      isCompleted: {
        needs: { status: true },
        compute(treatment) {
          return treatment.status === TreatmentStatus.COMPLETED;
        },
      },

      // Check if treatment is pending
      isPending: {
        needs: { status: true },
        compute(treatment) {
          return treatment.status === TreatmentStatus.PENDING;
        },
      },

      // Get formatted cost
      formattedCost: {
        needs: { cost: true },
        compute(treatment) {
          return `$${treatment.cost.toFixed(2)}`;
        },
      },

      // Get status display name
      statusDisplay: {
        needs: { status: true },
        compute(treatment) {
          switch (treatment.status) {
            case TreatmentStatus.PENDING:
              return 'Pending';
            case TreatmentStatus.COMPLETED:
              return 'Completed';
            default:
              return 'Unknown';
          }
        },
      },
    },
  },
});