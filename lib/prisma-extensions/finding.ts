import { Prisma } from "@prisma/client";

interface CreateFindingInput {
  toothId: number;
  description: string;
  recordedById?: number;
}

export const findingExtension = Prisma.defineExtension({
  name: "findingExtension",
  model: {
    finding: {
      // Create finding with tenant isolation inferred from the tooth
      async createFinding(toothId: number, description: string, recordedById?: number) {
        if (!description || !description.trim()) {
          throw new Error("Description is required");
        }

        // Look up tooth to infer tenantId and validate existence
        const prisma = Prisma.getExtensionContext(this) as any;
        const tooth = await prisma.tooth.findFirst({ where: { id: toothId } });
        if (!tooth) {
          throw new Error("Tooth not found");
        }

        try {
          return await this.create({
            data: {
              tenantId: tooth.tenantId,
              toothId,
              description: description.trim(),
              recordedById,
              ...(recordedById ? { createdById: recordedById } : {}),
            },
          });
        } catch (err: any) {
          // Surface underlying issue to caller for easier debugging
          throw new Error(`createFinding failed: ${err?.message || String(err)}`);
        }
      },

      // Find a finding with its treatments included
      async findWithTreatments(findingId: number) {
        return this.findUnique({
          where: { id: findingId },
          include: {
            treatments: true,
          },
        });
      },

      // Update description text for a finding
      async updateDescription(findingId: number, description: string, updatedById?: number) {
        if (!description || !description.trim()) {
          throw new Error("Description is required");
        }

        // Ensure the finding exists
        const existing = await this.findUnique({ where: { id: findingId } });
        if (!existing) {
          throw new Error("Finding not found");
        }

        return this.update({
          where: { id: findingId },
          data: {
            description: description.trim(),
            ...(updatedById ? { updatedById } : {}),
          },
        });
      },
    },
  },
});