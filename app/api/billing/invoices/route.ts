import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"

// POST /api/billing/invoices
// Body: { patientId: number, treatmentIds?: number[] }
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const patientId = Number(body?.patientId)
    const ids = Array.isArray(body?.treatmentIds) ? body.treatmentIds.map((x: any) => Number(x)).filter((n: number) => Number.isInteger(n) && n > 0) : []
    if (!Number.isInteger(patientId) || patientId <= 0) {
      return NextResponse.json({ success: false, message: "patientId must be a positive integer" }, { status: 400 })
    }

    const patient = await prisma.patient.findUnique({ where: { id: patientId } })
    if (!patient) return NextResponse.json({ success: false, message: "Patient not found" }, { status: 404 })

    // Fetch candidate treatments that belong to the patient's case sheet and are not linked to any invoice yet
    let treatments = [] as { id: number; cost: any }[]
    if (ids.length > 0) {
      treatments = await prisma.treatment.findMany({
        where: {
          id: { in: ids },
          invoiceId: null,
          finding: { tooth: { caseSheet: { patientId } } },
        },
        select: { id: true, cost: true },
      })
    } else {
      treatments = await prisma.treatment.findMany({
        where: {
          invoiceId: null,
          finding: { tooth: { caseSheet: { patientId } } },
        },
        select: { id: true, cost: true },
      })
    }

    const totalAmount = treatments.reduce((sum, t) => sum + Number(t.cost as any), 0)

    // If there are no eligible treatments to invoice:
    // 1) If specific treatmentIds were provided and they are already invoiced under a single invoice, return that invoice
    // 2) Otherwise, try to return an existing open invoice (DRAFT/SENT) for this patient
    // 3) If none found, do NOT create a new empty invoice; return 400 to avoid empty invoices
    if (treatments.length === 0) {
      // Case 1: All provided treatments already belong to a single invoice? Return it.
      if (ids.length > 0) {
        const selectedTreatments = await prisma.treatment.findMany({
          where: { id: { in: ids } },
          select: { invoiceId: true },
        })
        const invoiceIds = Array.from(
          new Set(
            selectedTreatments
              .map((t: { invoiceId: number | null }) => t.invoiceId)
              .filter((v: number | null): v is number => typeof v === 'number')
          )
        )
        if (invoiceIds.length === 1) {
          const existing = await prisma.invoice.findUnique({ where: { id: invoiceIds[0] } })
          if (existing) {
            return NextResponse.json({ success: true, invoice: existing }, { status: 200 })
          }
        }
      }

      // Case 2: Return an existing open invoice for the patient if any
      const openExisting = await prisma.invoice.findFirst({
        where: {
          patientId,
          OR: [{ status: "DRAFT" as any }, { status: "SENT" as any }, { status: "PARTIALLY_PAID" as any }],
        },
        orderBy: { invoiceDate: 'desc' },
      })
      if (openExisting) {
        return NextResponse.json({ success: true, invoice: openExisting }, { status: 200 })
      }

      // Case 3: Nothing to invoice and no open invoice to return
      return NextResponse.json({ success: false, message: "No eligible treatments to invoice" }, { status: 400 })
    }

    // Create invoice via extension which handles serial generation
    const invoice = await prisma.invoice.createInvoice({
      tenantId: (patient as any).tenantId as string,
      patientId,
      invoiceDate: new Date(),
      totalAmount: totalAmount as any,
    } as any)

    if (treatments.length > 0) {
      await prisma.treatment.updateMany({
        where: { id: { in: treatments.map((t) => t.id) } },
        data: { invoiceId: invoice.id },
      })
    }

    return NextResponse.json({ success: true, invoice }, { status: 201 })
  } catch (e) {
    return NextResponse.json({ success: false, message: "Failed to create invoice" }, { status: 500 })
  }
}

// GET /api/billing/invoices
// Returns a list of invoices with basic patient info
export async function GET(_request: NextRequest) {
  try {
    const invoices = await prisma.invoice.findMany({
      include: { patient: true },
      orderBy: { invoiceDate: "desc" },
    })
    return NextResponse.json(invoices, { status: 200 })
  } catch (e) {
    return NextResponse.json({ success: false, message: "Failed to load invoices" }, { status: 500 })
  }
}

export async function PUT() {
  return NextResponse.json({ success: false, message: "Method not allowed" }, { status: 405 })
}

export async function DELETE() {
  return NextResponse.json({ success: false, message: "Method not allowed" }, { status: 405 })
}


