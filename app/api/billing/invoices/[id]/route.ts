import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"

// GET /api/billing/invoices/[id]
export async function GET(_request: NextRequest, context: { params: { id: string } }) {
  const id = Number(context.params?.id)
  if (!Number.isInteger(id) || id <= 0) return NextResponse.json({ success: false, message: "Invalid invoice id" }, { status: 400 })
  const invoice = await prisma.invoice.findUnique({ where: { id }, include: { patient: true, payments: true, treatments: true } })
  if (!invoice) return NextResponse.json({ success: false, message: "Invoice not found" }, { status: 404 })
  return NextResponse.json(invoice, { status: 200 })
}

// PATCH /api/billing/invoices/[id]
export async function PATCH(request: NextRequest, context: { params: { id: string } }) {
  try {
    const id = Number(context.params?.id)
    if (!Number.isInteger(id) || id <= 0) return NextResponse.json({ success: false, message: "Invalid invoice id" }, { status: 400 })
    const body = await request.json()

    const updates: any = {}
    if (typeof body?.status === "string") updates.status = body.status.toUpperCase()
    if (Array.isArray(body?.treatmentIds)) {
      const ids = body.treatmentIds.map((x: any) => Number(x)).filter((n: number) => Number.isInteger(n) && n > 0)
      await prisma.treatment.updateMany({ where: { id: { in: ids } }, data: { invoiceId: id } })
      // Recalc totals
      const treatments = await prisma.treatment.findMany({ where: { invoiceId: id } })
      const total = treatments.reduce((sum: number, t: { cost: any }) => sum + Number(t.cost as any), 0)
      const inv = await prisma.invoice.findUnique({ where: { id } })
      if (inv) {
        updates.totalAmount = total as any
        updates.balanceDue = (total - Number(inv.amountPaid as any)) as any
      }
    }

    const updated = await prisma.invoice.update({ where: { id }, data: updates })
    return NextResponse.json({ success: true, invoice: updated }, { status: 200 })
  } catch {
    return NextResponse.json({ success: false, message: "Failed to update invoice" }, { status: 500 })
  }
}

// DELETE /api/billing/invoices/[id]
export async function DELETE(_request: NextRequest, context: { params: { id: string } }) {
  try {
    const id = Number(context.params?.id)
    if (!Number.isInteger(id) || id <= 0) return NextResponse.json({ success: false, message: "Invalid invoice id" }, { status: 400 })
    // Unlink treatments then delete invoice
    await prisma.$transaction([
      prisma.treatment.updateMany({ where: { invoiceId: id }, data: { invoiceId: null } }),
      prisma.invoice.delete({ where: { id } }),
    ])
    return NextResponse.json({ success: true }, { status: 200 })
  } catch {
    return NextResponse.json({ success: false, message: "Failed to delete invoice" }, { status: 500 })
  }
}


