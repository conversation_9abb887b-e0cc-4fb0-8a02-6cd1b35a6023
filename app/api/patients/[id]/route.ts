import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"

// GET /api/patients/[id]
export async function GET(_request: NextRequest, context: { params: { id: string } }) {
  try {
    const id = Number(context.params?.id)
    if (!Number.isInteger(id) || id <= 0) return NextResponse.json({ success: false, message: "Invalid patient id" }, { status: 400 })

    const p = await prisma.patient.findUnique({ where: { id }, include: { caseSheet: true } })
    if (!p) return NextResponse.json({ success: false, message: "Patient not found" }, { status: 404 })

    return NextResponse.json(
      {
        success: true,
        patient: {
          id: p.id,
          firstName: p.firstName,
          lastName: p.lastName,
          phoneNumber: p.phoneNumber,
          email: p.email,
          address: p.address,
          dateOfBirth: p.dateOfBirth,
          status: p.status,
          caseSheetId: p.caseSheet?.id ?? null,
        },
      },
      { status: 200 }
    )
  } catch (e) {
    return NextResponse.json({ success: false, message: "Failed to fetch patient" }, { status: 500 })
  }
}

// DELETE /api/patients/[id]
export async function DELETE(_request: NextRequest, context: { params: { id: string } }) {
  try {
    const id = Number(context.params?.id)
    if (!Number.isInteger(id) || id <= 0) return NextResponse.json({ success: false, message: "Invalid patient id" }, { status: 400 })

    // Basic safety: also delete related case sheet if exists; payments/invoices remain for audit
    await prisma.$transaction([
      prisma.caseSheet.deleteMany({ where: { patientId: id } }),
      prisma.patient.delete({ where: { id } }),
    ])

    return NextResponse.json({ success: true }, { status: 200 })
  } catch (e) {
    return NextResponse.json({ success: false, message: "Failed to delete patient" }, { status: 500 })
  }
}

// PATCH /api/patients/[id]
export async function PATCH(request: NextRequest, context: { params: { id: string } }) {
  try {
    const id = Number(context.params?.id)
    if (!Number.isInteger(id) || id <= 0) return NextResponse.json({ success: false, message: "Invalid patient id" }, { status: 400 })

    const body = await request.json().catch(() => ({}))
    const {
      firstName,
      lastName,
      phoneNumber,
      email,
      address,
      dateOfBirth,
    } = body ?? {}

    // Basic validation similar to create
    const errors: string[] = []
    if (!firstName || !String(firstName).trim()) errors.push("firstName is required")
    if (!lastName || !String(lastName).trim()) errors.push("lastName is required")
    if (!phoneNumber || !String(phoneNumber).trim()) errors.push("phoneNumber is required")
    if (email && !String(email).includes("@")) errors.push("email is invalid")
    if (errors.length > 0) {
      return NextResponse.json({ success: false, message: "Validation failed", errors }, { status: 400 })
    }

    const updated = await prisma.patient.update({
      where: { id },
      data: {
        firstName: String(firstName).trim(),
        lastName: String(lastName).trim(),
        phoneNumber: String(phoneNumber).trim(),
        email: email ? String(email).trim().toLowerCase() : null,
        address: address ? String(address).trim() : null,
        dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
      },
      include: { caseSheet: true },
    })

    return NextResponse.json(
      {
        success: true,
        patient: {
          id: updated.id,
          firstName: updated.firstName,
          lastName: updated.lastName,
          phoneNumber: updated.phoneNumber,
          email: updated.email,
          address: updated.address,
          dateOfBirth: updated.dateOfBirth,
          status: updated.status,
          caseSheetId: updated.caseSheet?.id ?? null,
        },
      },
      { status: 200 }
    )
  } catch (e) {
    return NextResponse.json({ success: false, message: "Failed to update patient" }, { status: 500 })
  }
}


