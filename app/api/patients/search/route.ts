import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import type { Patient, CaseSheet } from "@prisma/client";
import { AuthMiddleware } from "@/lib/auth/services";

// POST /api/patients/search
export const POST = AuthMiddleware.withAuth(
  async (request: NextRequest, { authContext }) => {
    try {
      const body = await request.json();
      const { patientId, phoneNumber, searchTerm, name } = body ?? {};

      const tenantIdFromAuth = authContext?.user?.tenantId ?? authContext?.tenant?.id;
      const tenantIdFromHeader = request.headers.get("x-tenant-id");
      const tenantIdFromBody = (body?.tenantId as string | undefined) ?? "";
      const tenantId = String(tenantIdFromAuth ?? tenantIdFromHeader ?? tenantIdFromBody ?? "").trim();

      const errors: string[] = [];
      if (!tenantId) errors.push("tenantId is required");
      if (!patientId && !phoneNumber && !searchTerm && !name) {
        errors.push("Provide one of: patientId, phoneNumber, or searchTerm/name");
      }
      if (errors.length > 0) {
        return NextResponse.json(
          { success: false, message: "Validation failed", errors },
          { status: 400 }
        );
      }

      const trimmedTenantId = tenantId;

    // Search by ID
    if (patientId) {
      const idNum = Number(patientId);
      if (!Number.isInteger(idNum) || idNum <= 0) {
        return NextResponse.json(
          { success: false, message: "patientId must be a positive integer" },
          { status: 400 }
        );
      }

      const patient = await prisma.patient.findFirst({
        where: { id: idNum, tenantId: trimmedTenantId },
        include: { caseSheet: true },
      });

      const patients = patient
        ? [
            {
              id: patient.id,
              firstName: patient.firstName,
              lastName: patient.lastName,
              phoneNumber: patient.phoneNumber,
              email: patient.email,
              caseSheetId: patient.caseSheet?.id ?? null,
              caseSheetStatus: patient.caseSheet?.status ?? null,
            },
          ]
        : [];

      return NextResponse.json({ success: true, patients }, { status: 200 });
    }

    // Search by phoneNumber
    if (phoneNumber) {
      const found = await prisma.patient.findByPhoneNumber(String(phoneNumber).trim(), trimmedTenantId);
      if (!found) {
        return NextResponse.json({ success: true, patients: [] }, { status: 200 });
      }

      const patient = await prisma.patient.findUnique({
        where: { id: found.id },
        include: { caseSheet: true },
      });

      const patients = patient
        ? [
            {
              id: patient.id,
              firstName: patient.firstName,
              lastName: patient.lastName,
              phoneNumber: patient.phoneNumber,
              email: patient.email,
              caseSheetId: patient.caseSheet?.id ?? null,
              caseSheetStatus: patient.caseSheet?.status ?? null,
            },
          ]
        : [];

      return NextResponse.json({ success: true, patients }, { status: 200 });
    }

    // Search by name/searchTerm
    const term = String(searchTerm ?? name ?? "").trim();
    const basics: Array<Pick<Patient, "id">> = await prisma.patient.searchByName(term, trimmedTenantId);

    if (basics.length === 0) {
      return NextResponse.json({ success: true, patients: [] }, { status: 200 });
    }

    const ids = basics.map((p: Pick<Patient, "id">) => p.id);
    const detailed: Array<Patient & { caseSheet: CaseSheet | null }> = await prisma.patient.findMany({
      where: { id: { in: ids } },
      include: { caseSheet: true },
      orderBy: [{ lastName: "asc" }, { firstName: "asc" }],
    });

    const patients = detailed.map((p: Patient & { caseSheet: CaseSheet | null }) => ({
      id: p.id,
      firstName: p.firstName,
      lastName: p.lastName,
      phoneNumber: p.phoneNumber,
      email: p.email,
      caseSheetId: p.caseSheet?.id ?? null,
      caseSheetStatus: p.caseSheet?.status ?? null,
    }));

      return NextResponse.json({ success: true, patients }, { status: 200 });
    } catch {
      return NextResponse.json(
        { success: false, message: "Failed to search patients" },
        { status: 500 }
      );
    }
  },
  { requireAuth: true, requireTenant: true }
);

export const GET = AuthMiddleware.withAuth(
  async (request: NextRequest, { authContext }) => {
    try {
      const url = new URL(request.url);
      const limitParam = url.searchParams.get("limit");
      const limitRaw = Number(limitParam);
      const limit = Number.isFinite(limitRaw) ? Math.min(Math.max(limitRaw, 1), 100) : 50;

      const tenantIdFromAuth = authContext?.user?.tenantId ?? authContext?.tenant?.id;
      const tenantIdFromHeader = request.headers.get("x-tenant-id");
      const tenantId = String(tenantIdFromAuth ?? tenantIdFromHeader ?? "").trim();

      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "tenantId is required" },
          { status: 400 }
        );
      }

      const detailed: Array<Patient & { caseSheet: CaseSheet | null }> = await prisma.patient.findMany({
        where: { tenantId },
        include: { caseSheet: true },
        orderBy: [{ createdAt: "desc" }],
        take: limit,
      });

      const patients = detailed.map((p: Patient & { caseSheet: CaseSheet | null }) => ({
        id: p.id,
        firstName: p.firstName,
        lastName: p.lastName,
        phoneNumber: p.phoneNumber,
        email: p.email,
        caseSheetId: p.caseSheet?.id ?? null,
        caseSheetStatus: p.caseSheet?.status ?? null,
        lastVisitDate: (p.caseSheet as any)?.lastVisitDate ?? null,
      }));

      return NextResponse.json({ success: true, patients }, { status: 200 });
    } catch {
      return NextResponse.json(
        { success: false, message: "Failed to list patients" },
        { status: 500 }
      );
    }
  },
  { requireAuth: true, requireTenant: true }
);

export async function PUT() {
  return NextResponse.json(
    { success: false, message: "Method not allowed" },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: "Method not allowed" },
    { status: 405 }
  );
}
