import { NextRequest, NextResponse } from "next/server";
import { AuthMiddleware, RoleGuard } from "@/lib/auth/services";
import { TenantContextManager } from "@/lib/tenant-context";
import prisma from "@/lib/prisma";
import { Prisma } from "@prisma/client";

/**
 * Dashboard API endpoint - Protected route example
 * GET /api/dashboard
 * 
 * This endpoint demonstrates:
 * - Authentication middleware usage
 * - Tenant context integration
 * - Role-based access control
 */

// Using the middleware wrapper for automatic auth handling
export const GET = AuthMiddleware.withAuth(
  async (request: NextRequest, { params: _params, authContext }) => {
    try {
      console.log(`[DASHBOARD-API] Request from authenticated user: ${authContext.user?.id}`);
      
      // Get tenant context (automatically set by middleware)
      const tenantContext = TenantContextManager.getCurrentContext();
      console.log(`[DASHBOARD-API] Current tenant context: ${JSON.stringify(tenantContext)}`);

      // Example: Get user's dashboard data based on their role
      type DashboardPermissions = {
        canManageUsers: boolean
        isAdmin: boolean
        isClinicalStaff: boolean
      }
      type DashboardStats = {
        todayPatients: number
        pendingTreatments: number
        outstandingInvoices: number
        generatedAt: string
      }
      type Activity = {
        id: string
        type: string
        description: string
        timestamp: string
        user: string
        relatedEntity?: { type: "patient" | "invoice" | "treatment"; id: number | string; name: string }
      }
      type DashboardData = {
        user: {
          id: number | string | null | undefined
          username: string | null | undefined
          userType: string | undefined
          tenantId: string | null | undefined
        }
        tenant: unknown
        permissions: DashboardPermissions
        navigation: Array<{ label: string; path: string }>
        stats?: DashboardStats
        recentActivity?: Activity[]
      }

      const dashboardData: DashboardData = {
        user: {
          id: authContext.user?.id,
          username: authContext.user?.username,
          userType: authContext.user?.userType,
          tenantId: authContext.user?.tenantId,
        },
        tenant: authContext.tenant,
        permissions: {
          canManageUsers: RoleGuard.canManageUsers(authContext.user?.userType || "RECEPTIONIST"),
          isAdmin: RoleGuard.isAdmin(authContext.user?.userType || "RECEPTIONIST"),
          isClinicalStaff: RoleGuard.isClinicalStaff(authContext.user?.userType || "RECEPTIONIST"),
        },
        navigation: [],
      };

      // Customize navigation based on user role
      switch (authContext.user?.userType) {
        case "SUPERUSER":
        case "ADMIN":
          dashboardData.navigation = [
            { label: "Dashboard", path: "/dashboard" },
            { label: "Users", path: "/admin/users" },
            { label: "Tenants", path: "/admin/tenants" },
            { label: "Patients", path: "/patients" },
            { label: "Appointments", path: "/appointments" },
            { label: "Settings", path: "/admin/settings" },
          ];
          break;
        
        case "DENTIST":
          dashboardData.navigation = [
            { label: "Dashboard", path: "/dashboard" },
            { label: "Patients", path: "/patients" },
            { label: "Appointments", path: "/appointments" },
            { label: "Treatments", path: "/treatments" },
            { label: "Reports", path: "/reports" },
          ];
          break;
        
        case "HYGIENIST":
          dashboardData.navigation = [
            { label: "Dashboard", path: "/dashboard" },
            { label: "Patients", path: "/patients" },
            { label: "Appointments", path: "/appointments" },
            { label: "Hygiene", path: "/hygiene" },
          ];
          break;
        
        case "ASSISTANT":
        case "RECEPTIONIST":
          dashboardData.navigation = [
            { label: "Dashboard", path: "/dashboard" },
            { label: "Patients", path: "/patients" },
            { label: "Appointments", path: "/appointments" },
            { label: "Schedule", path: "/schedule" },
          ];
          break;
      }

      // Real tenant-specific stats
      const tenantId = tenantContext?.tenantId || authContext.user?.tenantId
      if (!tenantId) {
        return NextResponse.json(
          { success: false, message: "Missing tenant context" },
          { status: 400 }
        )
      }

      // Establish today's start/end (UTC)
      const now = new Date()
      const startOfDay = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0))
      const endOfDay = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 23, 59, 59, 999))

      // 1) Today's patients: distinct patients with an appointment today (excluding cancelled)
      const todaysPatientIds = await prisma.appointment.findMany({
        where: {
          tenantId,
          appointmentDate: {
            gte: startOfDay,
            lte: endOfDay,
          },
          status: { not: "CANCELLED" },
        },
        distinct: ["patientId"],
        select: { patientId: true },
      })
      const todayPatients = todaysPatientIds.length

      // 2) Pending treatments
      const pendingTreatments = await prisma.treatment.count({
        where: {
          tenantId,
          status: "PENDING",
        },
      })

      // 3) Outstanding invoices (balanceDue > 0)
      const outstandingInvoices = await prisma.invoice.count({
        where: {
          tenantId,
          balanceDue: { gt: new Prisma.Decimal(0) },
        },
      })

      dashboardData.stats = {
        todayPatients,
        pendingTreatments,
        outstandingInvoices,
        generatedAt: now.toISOString(),
      };

      // Build recent activity from latest records
      type LatestPatient = {
        id: number
        firstName: string
        lastName: string
        createdAt: Date
        createdById: number | null
      }
      type LatestTreatment = {
        id: number
        procedureName: string
        completedDate: Date | null
        completedById: number | null
        finding: {
          tooth: {
            caseSheet: {
              patient: { id: number; firstName: string; lastName: string }
            }
          }
        }
      }
      type LatestPayment = {
        id: number
        amount: Prisma.Decimal
        createdAt: Date
        createdById: number | null
        patient: { id: number; firstName: string; lastName: string }
        invoice: { id: number; serial: string } | null
      }
      type LatestInvoice = {
        id: number
        serial: string
        updatedAt: Date
        status: "SENT" | "PAID"
        updatedById: number | null
        patient: { id: number; firstName: string; lastName: string }
      }

      const [latestPatients, latestTreatments, latestPayments, latestInvoices] = await Promise.all([
        prisma.patient.findMany({
          where: { tenantId },
          orderBy: { createdAt: "desc" },
          take: 5,
          select: { id: true, firstName: true, lastName: true, createdAt: true, createdById: true },
        }) as Promise<LatestPatient[]>,
        prisma.treatment.findMany({
          where: { tenantId, status: "COMPLETED" },
          orderBy: { completedDate: "desc" },
          take: 5,
          select: {
            id: true,
            procedureName: true,
            completedDate: true,
            completedById: true,
            finding: {
              select: {
                tooth: {
                  select: {
                    caseSheet: {
                      select: {
                        patient: { select: { id: true, firstName: true, lastName: true } },
                      },
                    },
                  },
                },
              },
            },
          },
        }) as Promise<LatestTreatment[]>,
        prisma.payment.findMany({
          where: { tenantId },
          orderBy: { createdAt: "desc" },
          take: 5,
          select: {
            id: true,
            amount: true,
            createdAt: true,
            createdById: true,
            patient: { select: { id: true, firstName: true, lastName: true } },
            invoice: { select: { id: true, serial: true } },
          },
        }) as Promise<LatestPayment[]>,
        prisma.invoice.findMany({
          where: { tenantId, status: { in: ["SENT", "PAID"] } },
          orderBy: { updatedAt: "desc" },
          take: 5,
          select: {
            id: true,
            serial: true,
            updatedAt: true,
            status: true,
            updatedById: true,
            patient: { select: { id: true, firstName: true, lastName: true } },
          },
        }) as Promise<LatestInvoice[]>,
      ])

      // Collect userIds for attribution
      const userIds = new Set<number>()
      latestPatients.forEach((p) => { if (p.createdById) userIds.add(p.createdById) })
      latestTreatments.forEach((t) => { if (t.completedById) userIds.add(t.completedById) })
      latestPayments.forEach((p) => { if (p.createdById) userIds.add(p.createdById) })
      latestInvoices.forEach((i) => { if (i.updatedById) userIds.add(i.updatedById) })

      type SimpleUser = { id: number; firstName: string | null; lastName: string | null; username: string | null }
      const users: SimpleUser[] = userIds.size > 0
        ? await prisma.user.findMany({
            where: { id: { in: Array.from(userIds) } },
            select: { id: true, firstName: true, lastName: true, username: true },
          })
        : []
      const userIdToDisplayName = new Map<number, string>(
        users.map((u: SimpleUser) => [
          u.id,
          [u.firstName, u.lastName].filter(Boolean).join(" ") || u.username || `User #${u.id}`,
        ])
      )

      const activities: Activity[] = []

      latestPatients.forEach((p: LatestPatient) => activities.push({
        id: `p-${p.id}`,
        type: "patient_created",
        description: `New patient created: ${p.firstName} ${p.lastName}`,
        timestamp: p.createdAt.toISOString(),
        user: p.createdById ? userIdToDisplayName.get(p.createdById) || "System" : "System",
        relatedEntity: { type: "patient", id: p.id, name: `${p.firstName} ${p.lastName}` },
      }))

      latestTreatments.forEach((t: LatestTreatment) => {
        const patient = t.finding?.tooth?.caseSheet?.patient
        const patientName = patient ? `${patient.firstName} ${patient.lastName}` : undefined
        activities.push({
          id: `t-${t.id}`,
          type: "treatment_completed",
          description: `Treatment completed`,
          timestamp: (t.completedDate ?? new Date()).toISOString(),
          user: t.completedById ? userIdToDisplayName.get(t.completedById) || "System" : "System",
          relatedEntity: patient
            ? { type: "patient", id: patient.id, name: patientName! }
            : { type: "treatment", id: t.id, name: t.procedureName },
        })
      })

      latestPayments.forEach((p: LatestPayment) => activities.push({
        id: `pay-${p.id}`,
        type: "payment_received",
        description: `Payment received`,
        timestamp: p.createdAt.toISOString(),
        user: p.createdById ? userIdToDisplayName.get(p.createdById) || "System" : "System",
        relatedEntity: p.invoice
          ? { type: "invoice", id: p.invoice.id, name: p.invoice.serial }
          : { type: "patient", id: p.patient.id, name: `${p.patient.firstName} ${p.patient.lastName}` },
      }))

      latestInvoices.forEach((i: LatestInvoice) => activities.push({
        id: `inv-${i.id}`,
        type: "invoice_sent",
        description: `${i.status === "PAID" ? "Invoice paid" : "Invoice sent"}: ${i.serial}`,
        timestamp: i.updatedAt.toISOString(),
        user: i.updatedById ? userIdToDisplayName.get(i.updatedById) || "System" : "System",
        relatedEntity: { type: "invoice", id: i.id, name: i.serial },
      }))

      // Sort by timestamp desc and take top 10
      activities.sort((a, b) => (a.timestamp < b.timestamp ? 1 : -1))
      dashboardData.recentActivity = activities.slice(0, 10)

      return NextResponse.json(
        {
          success: true,
          message: "Dashboard data retrieved successfully",
          data: dashboardData,
        },
        { status: 200 }
      );

    } catch (error) {
      console.error("[DASHBOARD-API] Error retrieving dashboard data:", error);
      
      return NextResponse.json(
        {
          success: false,
          message: "Failed to retrieve dashboard data",
          errors: ["An error occurred while loading dashboard"],
        },
        { status: 500 }
      );
    }
  },
  // Middleware options - require authentication for all user types
  RoleGuard.authenticatedOnly()
);

/**
 * Admin-only endpoint example
 */
export const POST = AuthMiddleware.withAuth(
  async (request: NextRequest, { params: _params, authContext }) => {
    try {
      const body = await request.json();
      
      console.log(`[DASHBOARD-API] Admin action by user: ${authContext.user?.id}`);
      
      // Admin-specific logic here
      return NextResponse.json(
        {
          success: true,
          message: "Admin action completed",
          data: { ...body, processedBy: authContext.user?.id },
        },
        { status: 200 }
      );

    } catch (error) {
      console.error("[DASHBOARD-API] Error in admin action:", error);
      
      return NextResponse.json(
        {
          success: false,
          message: "Failed to complete admin action",
          errors: ["An error occurred"],
        },
        { status: 500 }
      );
    }
  },
  // Require admin privileges
  RoleGuard.adminOnly()
);

/**
 * Clinical staff only endpoint example
 */
export const PUT = AuthMiddleware.withAuth(
  async (request: NextRequest, { params: _params, authContext }) => {
    try {
      const body = await request.json();
      
      console.log(`[DASHBOARD-API] Clinical action by user: ${authContext.user?.id}`);
      
      // Clinical staff logic here
      return NextResponse.json(
        {
          success: true,
          message: "Clinical action completed",
          data: { ...body, processedBy: authContext.user?.id },
        },
        { status: 200 }
      );

    } catch (error) {
      console.error("[DASHBOARD-API] Error in clinical action:", error);
      
      return NextResponse.json(
        {
          success: false,
          message: "Failed to complete clinical action",
          errors: ["An error occurred"],
        },
        { status: 500 }
      );
    }
  },
  // Require clinical staff privileges
  RoleGuard.clinicalStaffOnly()
);
