import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { AuthMiddleware } from "@/lib/auth/services";

// GET /api/clinical/case-sheets/[id]
export const GET = AuthMiddleware.withAuth(
  async (_request: NextRequest, context: { params?: Promise<Record<string, string>> | Record<string, string>; authContext: any }) => {
    try {
      const paramsObj = typeof (context as any).params?.then === 'function' ? await (context as any).params : ((context as any).params || ({} as Record<string, string>))
      const authContext = (context as any).authContext
      const idRaw = paramsObj?.id
      const caseSheetId = Number(idRaw)
      if (!Number.isInteger(caseSheetId) || caseSheetId <= 0) {
        return NextResponse.json(
          { success: false, message: "caseSheetId must be a positive integer" },
          { status: 400 }
        )
      }

      const resolvedTenantId = String(authContext?.user?.tenantId ?? authContext?.tenant?.id ?? "").trim()
      if (!resolvedTenantId) {
        return NextResponse.json(
          { success: false, message: "Missing tenant context" },
          { status: 400 }
        )
      }

      // Ensure tenant isolation
      const caseSheet = await prisma.caseSheet.findFirst({
        where: { id: caseSheetId, tenantId: resolvedTenantId },
        include: {
          patient: true,
          teeth: {
            include: {
              findings: {
                include: { treatments: true, recordedBy: true },
                orderBy: { recordedDate: "desc" },
              },
            },
            orderBy: { toothNumber: "asc" },
          },
        },
      })

      if (!caseSheet) {
        return NextResponse.json(
          { success: false, message: "Case sheet not found" },
          { status: 404 }
        )
      }

      return NextResponse.json({ success: true, caseSheet }, { status: 200 })
    } catch {
      return NextResponse.json(
        { success: false, message: "Failed to load case sheet" },
        { status: 500 }
      )
    }
  },
  { requireAuth: true, requireTenant: true }
)


