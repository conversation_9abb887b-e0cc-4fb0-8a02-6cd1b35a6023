import { NextRequest, NextResponse } from "next/server";
import { ClinicalWorkflowService, WorkflowError } from "@/lib/clinical/workflow-service";

// POST /api/clinical/case-sheets/[id]/initialize
export async function POST(_request: NextRequest, context: { params: { id: string } }) {
  try {
    const { id } = context.params || {} as { id: string };

    const errors: string[] = [];
    const patientId = Number(id);
    if (!Number.isInteger(patientId) || patientId <= 0) {
      errors.push("Patient ID in path must be a positive integer");
    }

    if (errors.length > 0) {
      return NextResponse.json(
        { success: false, message: "Validation failed", errors },
        { status: 400 }
      );
    }

    const service = new ClinicalWorkflowService();
    const { caseSheet, teethCount } = await service.createCaseSheetWithTeeth(patientId);

    return NextResponse.json(
      {
        success: true,
        message: "Case sheet initialized with complete dental chart",
        caseSheet,
        teethCount,
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof WorkflowError) {
      return NextResponse.json(
        { success: false, message: error.userMessage, step: error.step },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { success: false, message: "Failed to initialize case sheet" },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { success: false, message: "Method not allowed" },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { success: false, message: "Method not allowed" },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: "Method not allowed" },
    { status: 405 }
  );
}
