import { NextRequest, NextResponse } from "next/server";
import { ClinicalWorkflowService, WorkflowError } from "@/lib/clinical/workflow-service";

// POST /api/clinical/findings/[id]/treatments
export async function POST(request: NextRequest, context: { params: { id: string } }) {
  try {
    const { id } = context.params || ({} as { id: string });
    const body = await request.json();

    const errors: string[] = [];

    const findingId = Number(id);
    if (!Number.isInteger(findingId) || findingId <= 0) {
      errors.push("Finding ID in path must be a positive integer");
    }

    const procedureName = typeof body?.procedureName === "string" ? body.procedureName.trim() : "";
    if (!procedureName) {
      errors.push("procedureName is required");
    }

    const costProvided = body?.cost !== undefined && body?.cost !== null && String(body.cost).trim() !== "";
    if (!costProvided) {
      errors.push("cost is required");
    }

    if (errors.length > 0) {
      return NextResponse.json(
        { success: false, message: "Validation failed", errors },
        { status: 400 }
      );
    }

    const service = new ClinicalWorkflowService();
    const { treatment, invoice } = await service.createTreatmentWithInvoice({
      findingId,
      procedureName,
      cost: body.cost,
    });

    return NextResponse.json(
      {
        success: true,
        message: "Treatment created and invoice updated",
         treatment,
        invoice,
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof WorkflowError) {
      return NextResponse.json(
        { success: false, message: error.userMessage, step: error.step },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { success: false, message: "Failed to create treatment" },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { success: false, message: "Method not allowed" },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { success: false, message: "Method not allowed" },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: "Method not allowed" },
    { status: 405 }
  );
}
