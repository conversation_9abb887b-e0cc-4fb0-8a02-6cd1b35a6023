import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { AuthMiddleware } from "@/lib/auth/services";

// PATCH /api/clinical/findings/[id]
export const PATCH = AuthMiddleware.withAuth(
  async (request: NextRequest, context: { params?: Record<string, string>; authContext: any }) => {
    try {
      const params = context.params || ({} as Record<string, string>)
      const idRaw = params?.id
      const findingId = Number(idRaw)
      if (!Number.isInteger(findingId) || findingId <= 0) {
        return NextResponse.json({ success: false, message: "findingId must be a positive integer" }, { status: 400 })
      }

      const body = await request.json()
      const description = typeof body?.description === "string" ? body.description.trim() : ""
      const severityRaw = typeof body?.severity === "string" ? body.severity.trim().toLowerCase() : undefined
      const severity = severityRaw && ["low", "medium", "high"].includes(severityRaw) ? severityRaw : undefined
      if (!description) {
        return NextResponse.json({ success: false, message: "description is required" }, { status: 400 })
      }

      // Ensure record exists and tenant isolation by reading tenantId
      const existing = await prisma.finding.findUnique({ where: { id: findingId } })
      if (!existing) return NextResponse.json({ success: false, message: "Finding not found" }, { status: 404 })

      const updated = await prisma.finding.update({ where: { id: findingId }, data: { description, ...(severity ? { severity } : {}) } })
      return NextResponse.json({ success: true, finding: updated }, { status: 200 })
    } catch (e) {
      return NextResponse.json({ success: false, message: "Failed to update finding" }, { status: 500 })
    }
  },
  { requireAuth: true, requireTenant: true }
)

// DELETE /api/clinical/findings/[id]
export const DELETE = AuthMiddleware.withAuth(
  async (_request: NextRequest, context: { params?: Record<string, string>; authContext: any }) => {
    try {
      const params = context.params || ({} as Record<string, string>)
      const idRaw = params?.id
      const findingId = Number(idRaw)
      if (!Number.isInteger(findingId) || findingId <= 0) {
        return NextResponse.json({ success: false, message: "findingId must be a positive integer" }, { status: 400 })
      }

      const existing = await prisma.finding.findUnique({
        where: { id: findingId },
        include: {
          treatments: true,
          tooth: { include: { caseSheet: true } },
        },
      })
      if (!existing) return NextResponse.json({ success: false, message: "Finding not found" }, { status: 404 })

      const tenantId = (existing as any).tenantId as string | undefined
      const patientId = existing.tooth?.caseSheet?.patientId
      const totalTreatmentCost = existing.treatments.reduce((sum: number, t: { cost: any }) => sum + Number(t.cost as any), 0)

      // Delete treatments first, then finding
      await prisma.$transaction([
        prisma.treatment.deleteMany({ where: { findingId } }),
        prisma.finding.delete({ where: { id: findingId } }),
      ])

      // Adjust invoice totals for open invoice if applicable
      if (tenantId && patientId && totalTreatmentCost > 0) {
        const invoice = await prisma.invoice.findFirst({
          where: { patientId, tenantId, OR: [{ status: "DRAFT" as any }, { status: "SENT" as any }] },
        })
        if (invoice) {
          const newTotal = Number(invoice.totalAmount as any) - totalTreatmentCost
          const newBalance = newTotal - Number(invoice.amountPaid as any)
          await prisma.invoice.update({
            where: { id: invoice.id },
            data: {
              totalAmount: (newTotal < 0 ? 0 : newTotal) as any,
              balanceDue: (newBalance < 0 ? 0 : newBalance) as any,
              status: newTotal <= 0 ? ("DRAFT" as any) : invoice.status,
            },
          })
        }
      }

      return NextResponse.json({ success: true }, { status: 200 })
    } catch (e) {
      return NextResponse.json({ success: false, message: "Failed to delete finding" }, { status: 500 })
    }
  },
  { requireAuth: true, requireTenant: true }
)


