import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { AuthMiddleware } from "@/lib/auth/services";

// PATCH /api/clinical/treatments/[id]
export const PATCH = AuthMiddleware.withAuth(
  async (request: NextRequest, context: { params?: Promise<Record<string, string>> | Record<string, string>; authContext: any }) => {
    const paramsObj = typeof (context as any).params?.then === 'function' ? await (context as any).params : ((context as any).params || ({} as Record<string, string>))
    const authContext = (context as any).authContext
    try {
      const idRaw = paramsObj?.id
      const treatmentId = Number(idRaw)
      if (!Number.isInteger(treatmentId) || treatmentId <= 0) {
        return NextResponse.json({ success: false, message: "treatmentId must be a positive integer" }, { status: 400 })
      }

      const body = await request.json()
      const updateData: any = {}
      if (typeof body?.procedureName === "string") updateData.procedureName = body.procedureName.trim()
      if (body?.status) {
        const s = String(body.status).toLowerCase()
        updateData.status = s === "completed" ? "COMPLETED" : "PENDING"
        if (updateData.status === "COMPLETED") {
          updateData.completedDate = new Date()
          const userIdNum = Number((authContext?.user?.id as any) ?? NaN)
          if (Number.isFinite(userIdNum)) updateData.completedById = userIdNum
        } else {
          // If moving back to pending, clear completion info
          updateData.completedDate = null
          updateData.completedById = null
        }
      }
      const hasCost = body?.cost !== undefined && body?.cost !== null && String(body.cost).trim() !== ""
      const costNumber = hasCost ? Number(body.cost) : undefined
      if (hasCost && !Number.isFinite(costNumber)) {
        return NextResponse.json({ success: false, message: "cost must be a valid number" }, { status: 400 })
      }

      // Fetch existing treatment and context
      const existing = await prisma.treatment.findUnique({
        where: { id: treatmentId },
        include: {
          finding: {
            include: {
              tooth: { include: { caseSheet: true } },
            },
          },
        },
      })
      if (!existing) {
        return NextResponse.json({ success: false, message: "Treatment not found" }, { status: 404 })
      }

      const oldCost = Number(existing.cost as any)
      if (hasCost) updateData.cost = costNumber

      const updated = await prisma.treatment.update({ where: { id: treatmentId }, data: updateData })

      // Update open invoice totals if cost changed and patient context available
      if (hasCost && Number.isFinite(oldCost)) {
        const patientId = existing.finding?.tooth?.caseSheet?.patientId
        const tenantId = (existing as any).tenantId as string | undefined
        if (patientId && tenantId) {
          const invoice = await prisma.invoice.findFirst({
            where: { patientId, tenantId, OR: [{ status: "DRAFT" as any }, { status: "SENT" as any }] },
          })
          if (invoice) {
            const delta = Number(costNumber) - oldCost
            const newTotal = Number(invoice.totalAmount as any) + delta
            const newBalance = newTotal - Number(invoice.amountPaid as any)
            await prisma.invoice.update({
              where: { id: invoice.id },
              data: {
                totalAmount: (newTotal < 0 ? 0 : newTotal) as any,
                balanceDue: (newBalance < 0 ? 0 : newBalance) as any,
              },
            })
          }
        }
      }

      return NextResponse.json({ success: true, treatment: updated }, { status: 200 })
    } catch (e) {
      return NextResponse.json({ success: false, message: "Failed to update treatment" }, { status: 500 })
    }
  },
  { requireAuth: true, requireTenant: true }
)

// DELETE /api/clinical/treatments/[id]
export const DELETE = AuthMiddleware.withAuth(
  async (_request: NextRequest, context: { params?: Record<string, string>; authContext: any }) => {
    const params = context.params || ({} as Record<string, string>)
    const authContext = (context as any).authContext
    try {
      const idRaw = params?.id
      const treatmentId = Number(idRaw)
      if (!Number.isInteger(treatmentId) || treatmentId <= 0) {
        return NextResponse.json({ success: false, message: "treatmentId must be a positive integer" }, { status: 400 })
      }

      // Fetch existing with context for invoice adjustment
      const existing = await prisma.treatment.findUnique({
        where: { id: treatmentId },
        include: {
          finding: { include: { tooth: { include: { caseSheet: true } } } },
        },
      })
      if (!existing) {
        return NextResponse.json({ success: false, message: "Treatment not found" }, { status: 404 })
      }

      const oldCost = Number(existing.cost as any)
      const patientId = existing.finding?.tooth?.caseSheet?.patientId
      const tenantId = (existing as any).tenantId as string | undefined

      await prisma.treatment.delete({ where: { id: treatmentId } })

      // Adjust invoice totals if open invoice exists
      if (patientId && tenantId && Number.isFinite(oldCost)) {
        const invoice = await prisma.invoice.findFirst({
          where: { patientId, tenantId, OR: [{ status: "DRAFT" as any }, { status: "SENT" as any }] },
        })
        if (invoice) {
          const newTotal = Number(invoice.totalAmount as any) - oldCost
          const newBalance = newTotal - Number(invoice.amountPaid as any)
          await prisma.invoice.update({
            where: { id: invoice.id },
            data: {
              totalAmount: (newTotal < 0 ? 0 : newTotal) as any,
              balanceDue: (newBalance < 0 ? 0 : newBalance) as any,
              status: newTotal <= 0 ? ("DRAFT" as any) : invoice.status,
            },
          })
        }
      }

      return NextResponse.json({ success: true }, { status: 200 })
    } catch {
      return NextResponse.json({ success: false, message: "Failed to delete treatment" }, { status: 500 })
    }
  },
  { requireAuth: true, requireTenant: true }
)


