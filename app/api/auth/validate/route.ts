import { NextRequest, NextResponse } from "next/server";
import { AuthService } from "@/lib/auth/services/auth-service";

/**
 * Authentication Validation API endpoint
 * GET /api/auth/validate
 * 
 * Requirements: 3.4, 8.1
 * Validates current user session and returns user information
 */
export async function GET(request: NextRequest) {
  try {
    // Validate session using AuthService
    const sessionValidation = await AuthService.validateSession();
    
    if (!sessionValidation.isValid || !sessionValidation.session) {
      return NextResponse.json(
        {
          success: false,
          message: sessionValidation.message || "Invalid session",
          user: null,
        },
        { status: 401 }
      );
    }

    const session = sessionValidation.session;

    // Return user information (excluding sensitive data)
    const userInfo = {
      id: session.userId,
      username: session.username,
      userType: session.userType,
      tenantId: session.tenantId,
      isActive: session.isActive,
      // Note: We don't have email, firstName, lastName in session
      // These would need to be fetched from database if needed
      email: "", // Placeholder
      firstName: "", // Placeholder
      lastName: "", // Placeholder
    };

    return NextResponse.json(
      {
        success: true,
        message: "Session is valid",
        user: userInfo,
        session: {
          createdAt: session.createdAt,
          expiresAt: session.expiresAt,
        },
      },
      { status: 200 }
    );

  } catch (error) {
    console.error("[AUTH-VALIDATE] Error validating session:", error);
    
    return NextResponse.json(
      {
        success: false,
        message: "Authentication validation failed",
        user: null,
      },
      { status: 500 }
    );
  }
}

/**
 * Handle unsupported HTTP methods
 */
export async function POST() {
  return NextResponse.json(
    {
      success: false,
      message: "Method not allowed",
    },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      message: "Method not allowed",
    },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      message: "Method not allowed",
    },
    { status: 405 }
  );
}