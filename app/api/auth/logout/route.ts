import { NextRequest, NextResponse } from "next/server";
import { AuthService } from "@/lib/auth/services";
import { TenantContextManager } from "@/lib/tenant-context";

/**
 * Logout API endpoint
 * GET/POST /api/auth/logout
 * 
 * Requirements: 4.3
 * Destroys user session and clears authentication cookies
 */
async function handleLogout(request: NextRequest) {
  try {
    console.log("[LOGOUT] Processing logout request");

    // Validate current session (optional - logout should work even with invalid session)
    const sessionValidation = await AuthService.validateSession();
    
    if (sessionValidation.isValid && sessionValidation.session) {
      console.log(`[LOGOUT] Logging out user: ${sessionValidation.session.userId} (${sessionValidation.session.username})`);
    } else {
      console.log("[LOGOUT] No valid session found, proceeding with cleanup");
    }

    // Destroy session and clear cookies
    const destroyResult = await AuthService.destroySession();
    
    if (!destroyResult.success) {
      console.error("[LOGOUT] Failed to destroy session:", destroyResult.message);
      
      return NextResponse.json(
        {
          success: false,
          message: "Failed to logout",
          errors: ["Unable to clear session"],
        },
        { status: 500 }
      );
    }

    console.log("[LOGOUT] Session destroyed successfully");

    // Clear tenant context
    TenantContextManager.clearGlobalContext();
    console.log("[LOGOUT] Tenant context cleared");

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Logout successful",
        redirectUrl: "/auth/login",
      },
      { status: 200 }
    );

  } catch (error) {
    console.error("[LOGOUT] Unexpected error:", error);
    
    // Even if there's an error, we should try to clear the session
    try {
      await AuthService.destroySession();
    } catch (cleanupError) {
      console.error("[LOGOUT] Failed to cleanup session after error:", cleanupError);
    }
    
    // Don't expose internal errors to client, but still return success
    // since logout should always appear to work from user perspective
    return NextResponse.json(
      {
        success: true,
        message: "Logout completed",
        redirectUrl: "/auth/login",
      },
      { status: 200 }
    );
  }
}

export async function GET(request: NextRequest) {
  return handleLogout(request);
}

export async function POST(request: NextRequest) {
  return handleLogout(request);
}
