import { NextRequest, NextResponse } from "next/server";
import { TenantService, UserService } from "@/lib/auth/services";
import { registrationSchema } from "@/lib/auth/schemas";
import { SanitizationUtils, ValidationUtils, SecurityErrorHandler } from "@/lib/auth/utils";

/**
 * Registration API endpoint
 * POST /api/auth/register
 * 
 * Requirements: 1.1, 1.3, 1.4, 1.5, 2.1, 5.2, 5.4
 * Creates a new tenant and admin user for clinic registration
 */
export async function POST(request: NextRequest) {
  try {
    // Get client information for security logging
    const clientIP = request.headers.get('x-forwarded-for')?.split(',')[0] || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Parse request body with size limit
    const body = await request.json();
    
    // Check for basic security threats in raw input
    const rawInput = JSON.stringify(body);
    if (rawInput.length > 50000) {
      SecurityErrorHandler.logSecurityIncident(
        'OVERSIZED_REQUEST',
        `Request size: ${rawInput.length} bytes`,
        userAgent,
        clientIP
      );
      const errorResponse = SecurityErrorHandler.createSecurityErrorResponse(
        'Request too large',
        413
      );
      return NextResponse.json(errorResponse, { status: 413 });
    }

    const securityCheck = ValidationUtils.validateSecurityThreats(rawInput);
    if (!securityCheck.isValid) {
      SecurityErrorHandler.logSecurityIncident(
        'SECURITY_THREAT_DETECTED',
        `Threats: ${securityCheck.threats.join(', ')}`,
        userAgent,
        clientIP
      );
      const errorResponse = SecurityErrorHandler.createSecurityErrorResponse();
      return NextResponse.json(errorResponse, { status: 400 });
    }

    // Validate input using Zod schema
    const validationResult = registrationSchema.safeParse(body);
    
    if (!validationResult.success) {
      const errors = validationResult.error.issues.map((err) => 
        `${err.path.join('.')}: ${err.message}`
      );
      
      const errorResponse = SecurityErrorHandler.createValidationErrorResponse(
        "Registration data validation failed",
        errors
      );
      
      return NextResponse.json(errorResponse, { status: 400 });
    }

    const { clinic, adminUser } = validationResult.data;

    // Sanitize input data (additional layer of protection)
    const sanitizedClinicData = {
      name: SanitizationUtils.sanitizeString(clinic.name),
      address: SanitizationUtils.sanitizeString(clinic.address),
      phone: SanitizationUtils.sanitizePhone(clinic.phone),
      email: SanitizationUtils.sanitizeEmail(clinic.email),
    };

    const sanitizedAdminData = {
      username: SanitizationUtils.sanitizeString(adminUser.username),
      email: SanitizationUtils.sanitizeEmail(adminUser.email),
      firstName: SanitizationUtils.sanitizeString(adminUser.firstName),
      lastName: SanitizationUtils.sanitizeString(adminUser.lastName),
      password: adminUser.password, // Don't sanitize password
    };

    // Step 1: Create tenant
    console.log(`[REGISTRATION] Creating tenant for clinic: ${sanitizedClinicData.name}`);
    
    const tenantResult = await TenantService.createTenant(sanitizedClinicData);
    
    if (!tenantResult.success) {
      return NextResponse.json(
        {
          success: false,
          message: tenantResult.message,
          errors: tenantResult.errors,
        },
        { status: 400 }
      );
    }

    const tenantId = tenantResult.tenantId!;
    console.log(`[REGISTRATION] Tenant created successfully: ${tenantId}`);

    // Step 2: Create admin user with tenant context
    console.log(`[REGISTRATION] Creating admin user for tenant: ${tenantId}`);
    
    const adminUserData = {
      ...sanitizedAdminData,
      tenantId,
    };

    const userResult = await UserService.createAdminUser(adminUserData);
    
    if (!userResult.success) {
      // If user creation fails, we should ideally clean up the tenant
      // For now, we'll log the error and return the failure
      console.error(`[REGISTRATION] Failed to create admin user for tenant ${tenantId}:`, userResult.errors);
      
      return NextResponse.json(
        {
          success: false,
          message: userResult.message,
          errors: userResult.errors,
        },
        { status: 400 }
      );
    }

    const userId = userResult.userId!;
    console.log(`[REGISTRATION] Admin user created successfully: ${userId}`);

    // Step 3: Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Registration completed successfully. You can now log in with your credentials.",
        tenantId,
        userId,
      },
      { status: 201 }
    );

  } catch (error) {
    console.error("[REGISTRATION] Unexpected error:", error);
    
    // Don't expose internal errors to client
    return NextResponse.json(
      {
        success: false,
        message: "An unexpected error occurred during registration",
        errors: ["Please try again later"],
      },
      { status: 500 }
    );
  }
}

/**
 * Handle unsupported HTTP methods
 */
export async function GET() {
  return NextResponse.json(
    {
      success: false,
      message: "Method not allowed",
    },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      message: "Method not allowed",
    },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      message: "Method not allowed",
    },
    { status: 405 }
  );
}