"use client"

import * as React from "react"
import { usePara<PERSON>, useRouter } from "next/navigation"
import InvoiceDetails, { type InvoiceDetailsData } from "@/components/billing/InvoiceDetails"
import PaymentHistory, { type PaymentItem } from "@/components/billing/PaymentHistory"
import PaymentForm from "@/components/billing/PaymentForm"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ConfirmDialog } from "@/components/ui/ConfirmDialog"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Breadcrumbs } from "@/components/layout/Breadcrumbs"

export default function InvoiceDetailsPage() {
  const params = useParams<{ id: string }>()
  const router = useRouter()
  const invoiceId = params?.id
  // Invoices are not editable; ignore any edit flags
  const isEdit = false

  // Placeholder state. Replace with API fetch hooks.
  const [invoice, setInvoice] = React.useState<InvoiceDetailsData | null>(null)
  const [payments, setPayments] = React.useState<PaymentItem[]>([])
  const [openPayment, setOpenPayment] = React.useState(false)
  const [editPayment, setEditPayment] = React.useState<PaymentItem | null>(null)
  const [confirmDeleteOpen, setConfirmDeleteOpen] = React.useState(false)
  const [paymentToDelete, setPaymentToDelete] = React.useState<PaymentItem | null>(null)
  const [loading, setLoading] = React.useState(false)
  const [confirmOpen, setConfirmOpen] = React.useState(false)

  React.useEffect(() => {
    const load = async () => {
      if (!invoiceId) return
      const res = await fetch(`/api/billing/invoices/${invoiceId}`)
      if (!res.ok) return
      const inv = await res.json()
      const data: InvoiceDetailsData = {
        id: inv.id,
        serial: inv.serial,
        date: inv.invoiceDate,
        status: (String(inv.status).toLowerCase() as any),
        patient: { id: inv.patient?.id, name: `${inv.patient?.firstName ?? ''} ${inv.patient?.lastName ?? ''}`.trim() || '—' },
        treatments: (inv.treatments ?? []).map((t: any) => ({ id: t.id, procedureName: t.procedureName, status: 'completed', cost: Number(t.cost ?? 0) })),
        payments: (inv.payments ?? []).map((p: any) => ({ id: p.id, amount: Number(p.amount ?? 0), date: p.paymentDate, method: String(p.paymentMethod ?? '').toLowerCase() })),
        totalAmount: Number(inv.totalAmount ?? 0),
        amountPaid: Number(inv.amountPaid ?? 0),
        balanceDue: Number(inv.balanceDue ?? 0),
      }
      setInvoice(data)
      setPayments(data.payments ?? [])
    }
    load()
  }, [invoiceId])

  const handleProcessPayment = () => {
    setEditPayment(null)
    setOpenPayment(true)
  }

  const handlePaymentSubmit = async (data: any) => {
    if (!invoice) return
    setLoading(true)
    try {
      const methodMap: Record<string, string> = {
        cash: "CASH",
        card: "CARD",
        bank_transfer: "BANK_TRANSFER",
        insurance: "OTHER",
        other: "OTHER",
      }
      const payload = {
        patientId: Number(invoice.patient.id),
        amount: data.amount,
        paymentMethod: methodMap[String(data.paymentMethod) as keyof typeof methodMap] ?? "OTHER",
      }
      const url = `/api/billing/invoices/${invoice.id}/payments`
      const res = await fetch(url, {
        method: editPayment ? 'PUT' : 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(editPayment ? { id: editPayment.id, ...payload } : payload),
      })
      if (!res.ok) throw new Error('Failed to record payment')
      const result = await res.json()

      // Update UI from server response
      const savedPayment = result?.payment
      const updatedInv = result?.invoice
      let nextPayments = payments
      if (editPayment) {
        nextPayments = payments.map((p) => p.id === editPayment.id ? {
          ...p,
          amount: Number(savedPayment?.amount ?? data.amount),
          method: String(data.paymentMethod),
        } : p)
      } else {
        const newPayment: PaymentItem = {
          id: savedPayment?.id ?? `${Date.now()}`,
          amount: Number(savedPayment?.amount ?? data.amount),
          date: savedPayment?.paymentDate ?? data.paymentDate,
          method: String(data.paymentMethod),
        }
        nextPayments = [...payments, newPayment]
      }
      if (updatedInv) {
        const mappedStatus = String(updatedInv.status).toLowerCase() as InvoiceDetailsData['status']
        setInvoice({
          ...invoice,
          payments: nextPayments,
          amountPaid: Number(updatedInv.amountPaid ?? invoice.amountPaid ?? 0),
          balanceDue: Number(updatedInv.balanceDue ?? invoice.balanceDue ?? 0),
          status: mappedStatus,
        })
      } else {
        const amount = Number(data.amount)
        const delta = editPayment ? (amount - (payments.find(p => p.id === editPayment!.id)?.amount ?? 0)) : amount
        const nextPaid = (invoice.amountPaid ?? 0) + delta
        const nextBalance = Math.max(0, (invoice.totalAmount ?? 0) - nextPaid)
        const nextStatus: InvoiceDetailsData['status'] = nextBalance <= 0 ? "paid" : nextPaid > 0 ? "partially_paid" : (invoice.status === "draft" ? "sent" : invoice.status)
        setInvoice({ ...invoice, payments: nextPayments, amountPaid: nextPaid, balanceDue: nextBalance, status: nextStatus })
      }
      setPayments(nextPayments)
      setOpenPayment(false)
      setEditPayment(null)
    } catch (e) {
      console.error(e)
    } finally {
      setLoading(false)
    }
  }

  const handleEditPayment = (p: PaymentItem) => {
    setEditPayment(p)
    setOpenPayment(true)
  }

  const handleDeletePayment = async (paymentId: PaymentItem['id']) => {
    if (!invoice) return
    setLoading(true)
    try {
      const res = await fetch(`/api/billing/invoices/${invoice.id}/payments?id=${paymentId}`, {
        method: 'DELETE',
        credentials: 'include',
      })
      if (!res.ok) throw new Error('Failed to delete payment')
      const result = await res.json()
      const nextPayments = payments.filter(p => p.id !== paymentId)
      const updatedInv = result?.invoice
      if (updatedInv) {
        const mappedStatus = String(updatedInv.status).toLowerCase() as InvoiceDetailsData['status']
        setInvoice({
          ...invoice,
          payments: nextPayments,
          amountPaid: Number(updatedInv.amountPaid ?? 0),
          balanceDue: Number(updatedInv.balanceDue ?? 0),
          status: mappedStatus,
        })
      } else {
        // Fallback recalc
        const removed = payments.find(p => p.id === paymentId)
        const removedAmount = removed ? removed.amount : 0
        const nextPaid = Math.max(0, (invoice.amountPaid ?? 0) - removedAmount)
        const nextBalance = Math.max(0, (invoice.totalAmount ?? 0) - nextPaid)
        const nextStatus: InvoiceDetailsData['status'] = nextBalance <= 0 ? "paid" : nextPaid > 0 ? "partially_paid" : "sent"
        setInvoice({ ...invoice, payments: nextPayments, amountPaid: nextPaid, balanceDue: nextBalance, status: nextStatus })
      }
      setPayments(nextPayments)
    } catch (e) {
      console.error(e)
    } finally {
      setLoading(false)
    }
  }

  if (!invoice) return null

  return (
    <div className="space-y-6">
      <InvoiceDetails
        invoice={invoice}
        onEdit={undefined}
        onProcessPayment={handleProcessPayment}
        onSendInvoice={() => { /* wire to API */ }}
        onPaymentSuccess={() => { /* refresh if needed */ }}
        onAutoMarkPaid={() => { /* persist status change */ }}
        onCancelInvoice={() => setConfirmOpen(true)}
      />

      <Card>
        <CardHeader>
          <CardTitle>Payments</CardTitle>
        </CardHeader>
        <CardContent>
          <PaymentHistory
            payments={payments}
            invoiceTotal={invoice.totalAmount}
            onEdit={handleEditPayment}
            onDelete={(id) => {
              const p = payments.find((x) => x.id === id) || null
              setPaymentToDelete(p)
              setConfirmDeleteOpen(true)
            }}
          />
        </CardContent>
      </Card>

      <Dialog open={openPayment} onOpenChange={setOpenPayment}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Record Payment</DialogTitle>
          </DialogHeader>
          <PaymentForm
            loading={loading}
            onSubmit={handlePaymentSubmit}
            onCancel={() => setOpenPayment(false)}
            outstandingBalance={invoice.balanceDue}
            defaultValues={editPayment ? { amount: String(editPayment.amount), paymentMethod: (editPayment.method as any), paymentDate: typeof editPayment.date === 'string' ? editPayment.date : undefined } : undefined}
          />
        </DialogContent>
      </Dialog>

      <ConfirmDialog
        open={confirmOpen}
        onOpenChange={setConfirmOpen}
        title="Cancel invoice?"
        description="This will cancel and remove the invoice. This action cannot be undone."
        confirmText="Cancel invoice"
        destructive
        onConfirm={async () => {
          try {
            await fetch(`/api/billing/invoices/${invoice.id}`, { method: 'DELETE' })
            router.push('/dashboard/billing')
          } finally {
            setConfirmOpen(false)
          }
        }}
        onCancel={() => setConfirmOpen(false)}
      />

      <ConfirmDialog
        open={confirmDeleteOpen}
        onOpenChange={setConfirmDeleteOpen}
        title="Delete payment?"
        description="This will permanently delete the payment record and update the invoice totals. This action cannot be undone."
        confirmText="Delete"
        destructive
        onConfirm={async () => {
          try {
            if (!paymentToDelete) return
            await handleDeletePayment(paymentToDelete.id)
          } finally {
            setPaymentToDelete(null)
            setConfirmDeleteOpen(false)
          }
        }}
        onCancel={() => {
          setPaymentToDelete(null)
          setConfirmDeleteOpen(false)
        }}
      />
    </div>
  )
}


