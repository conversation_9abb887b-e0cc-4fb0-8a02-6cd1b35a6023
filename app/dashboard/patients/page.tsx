"use client"

import * as React from "react"
import { PatientSearchForm, type SearchCriteria } from "@/components/patients/PatientSearchForm"
import { PatientTable, type Patient } from "@/components/patients/PatientTable"
import { LoadingSpinner } from "@/components/ui/LoadingSpinner"
import { EmptyState } from "@/components/ui/EmptyState"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { PatientCreateModal } from "@/components/patients/PatientCreateModal"

async function searchPatients(criteria: SearchCriteria): Promise<Patient[]> {
  const body: any = {}
  if (criteria.searchType === "id") body.patientId = criteria.query
  else if (criteria.searchType === "phone") body.phoneNumber = criteria.query
  else body.name = criteria.query

  const res = await fetch("/api/patients/search", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(body),
  })
  if (!res.ok) return []
  const json = await res.json()
  return json.patients ?? []
}

export default function PatientsPage() {
  const [loading, setLoading] = React.useState(false)
  const [patients, setPatients] = React.useState<Patient[] | null>(null)
  const [createOpen, setCreateOpen] = React.useState(false)
  
  const loadInitialPatients = React.useCallback(async () => {
    setLoading(true)
    try {
      const res = await fetch('/api/patients/search?limit=50', {
        method: 'GET',
        headers: { 'Accept': 'application/json' },
      })
      if (!res.ok) return
      const json = await res.json()
      setPatients(json.patients ?? [])
    } finally {
      setLoading(false)
    }
  }, [])
  
  // Open create modal if query contains ?create=1
  React.useEffect(() => {
    try {
      const params = new URLSearchParams(window.location.search)
      const shouldOpen = params.get('create') === '1'
      if (shouldOpen) setCreateOpen(true)
    } catch {}
  }, [])

  // Initial load: fetch recent patients so the table is visible without a search
  React.useEffect(() => {
    loadInitialPatients()
  }, [])

  const handleSearch = async (criteria: SearchCriteria) => {
    setLoading(true)
    try {
      const result = await searchPatients(criteria)
      setPatients(result)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between gap-4">
            <div>
              <CardTitle>Patients</CardTitle>
              <CardDescription>Search and manage patient records.</CardDescription>
            </div>
            <Button onClick={() => setCreateOpen(true)}>Create Patient</Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <PatientSearchForm
              onSearch={handleSearch}
              onCreateNew={() => setCreateOpen(true)}
              loading={loading}
              showSearchButton={false}
              showCreateButton={false}
              showResetButton
              onResetToDefaultList={loadInitialPatients}
            />

            {loading && (
              <div className="flex justify-center py-10">
                <LoadingSpinner size="lg" label="Loading patients" />
              </div>
            )}

            {!loading && patients && patients.length === 0 && (
              <EmptyState title="No patients found" description="Adjust your search or create a new patient." />
            )}

            {!loading && patients && patients.length > 0 && (
              <PatientTable patients={patients} />
            )}
          </div>
        </CardContent>
      </Card>

      <PatientCreateModal open={createOpen} onOpenChange={setCreateOpen} />
    </div>
  )
}
