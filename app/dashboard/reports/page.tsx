"use client"

import * as React from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

type ReportType = "patients" | "billing" | "treatments"

export default function ReportsPage() {
  const [report, setReport] = React.useState<ReportType>("patients")
  const [dateFrom, setDateFrom] = React.useState<string>("")
  const [dateTo, setDateTo] = React.useState<string>("")
  const [query, setQuery] = React.useState<string>("")
  const [rows, setRows] = React.useState<any[]>([])
  const [loading, setLoading] = React.useState(false)

  const run = async () => {
    setLoading(true)
    // TODO: Replace with API
    await new Promise((r) => setTimeout(r, 400))
    if (report === "patients") {
      setRows([
        { id: 1, name: "John Doe", lastVisit: "2024-05-01", status: "Active" },
        { id: 2, name: "Jane Smith", lastVisit: "2024-04-20", status: "Active" },
      ])
    } else if (report === "billing") {
      setRows([
        { id: "1", date: "2024-05-02", patient: "John Doe", total: 220, balance: 0 },
        { id: "2", date: "2024-05-03", patient: "Jane Smith", total: 180, balance: 60 },
      ])
    } else {
      setRows([
        { id: 101, patient: "John Doe", procedure: "Filling", status: "Completed" },
        { id: 102, patient: "Jane Smith", procedure: "Cleaning", status: "Pending" },
      ])
    }
    setLoading(false)
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Reports</CardTitle>
          <CardDescription>Generate quick summaries for patients, billing, or treatments.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap items-end gap-2">
            <div className="flex flex-col">
              <label className="text-xs text-muted-foreground">Report</label>
              <Select value={report} onValueChange={(v) => setReport(v as ReportType)}>
                <SelectTrigger className="h-9 w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="patients">Patients</SelectItem>
                  <SelectItem value="billing">Billing</SelectItem>
                  <SelectItem value="treatments">Treatments</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col">
              <label className="text-xs text-muted-foreground">From</label>
              <Input type="date" value={dateFrom} onChange={(e) => setDateFrom(e.target.value)} className="h-9 w-44" />
            </div>
            <div className="flex flex-col">
              <label className="text-xs text-muted-foreground">To</label>
              <Input type="date" value={dateTo} onChange={(e) => setDateTo(e.target.value)} className="h-9 w-44" />
            </div>
            <div className="flex flex-col min-w-40">
              <label className="text-xs text-muted-foreground">Search</label>
              <Input value={query} onChange={(e) => setQuery(e.target.value)} placeholder="Name, invoice, procedure" className="h-9 w-64" />
            </div>
            <Button onClick={run} disabled={loading} className="ml-auto">
              {loading ? "Running..." : "Run report"}
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Results</CardTitle>
        </CardHeader>
        <CardContent>
          {rows.length === 0 ? (
            <div className="text-muted-foreground text-sm">No results yet. Select filters and run a report.</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  {Object.keys(rows[0]).map((key) => (
                    <TableHead key={key} className="capitalize">{key}</TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {rows.map((row, i) => (
                  <TableRow key={i}>
                    {Object.keys(row).map((key) => (
                      <TableCell key={key}>{String((row as any)[key])}</TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}


