"use client"

import * as React from "react"
import dynamic from "next/dynamic"
import type { DashboardMetricsData } from "@/components/dashboard/DashboardMetrics"
import type { ActivityItem } from "@/components/dashboard/RecentActivity"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Toaster } from "@/components/ui/sonner"

export default function DashboardPage() {
  const router = useRouter()
  const [metrics, setMetrics] = React.useState<DashboardMetricsData | null>(null)
  const [activities, setActivities] = React.useState<ActivityItem[]>([])
  const [loading, setLoading] = React.useState(true)
  const [error, setError] = React.useState<string | null>(null)

  const loadData = React.useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const res = await fetch('/api/dashboard', { credentials: 'include' })
      if (!res.ok) throw new Error('Failed to fetch metrics')
      const json = await res.json()
      const stats = json?.data?.stats
      const todayPatients = Number(stats?.todayPatients ?? 0)
      const pendingTreatments = Number(stats?.pendingTreatments ?? 0)
      const outstandingInvoices = Number(stats?.outstandingInvoices ?? 0)

      setMetrics({ todayPatients, pendingTreatments, outstandingInvoices })
      setActivities(Array.isArray(json?.data?.recentActivity) ? json.data.recentActivity : [])
    } catch (e) {
      setError("Failed to load dashboard data")
    } finally {
      setLoading(false)
    }
  }, [])

  React.useEffect(() => {
    loadData()
    // Example toast to demonstrate wiring; remove or adjust as needed
    // import dynamically to avoid SSR mismatch
    import("sonner").then(({ toast }) => {
      toast.success("Dashboard loaded", { description: "Metrics and recent activity are up to date." })
    }).catch(() => {})
  }, [loadData])

  const DashboardMetrics = React.useMemo(
    () => dynamic(() => import("@/components/dashboard/DashboardMetrics"), { ssr: false }),
    []
  )
  const QuickActions = React.useMemo(
    () => dynamic(() => import("@/components/dashboard/QuickActions"), { ssr: false }),
    []
  )
  const RecentActivity = React.useMemo(
    () => dynamic(() => import("@/components/dashboard/RecentActivity"), { ssr: false }),
    []
  )

  return (
    <div className="space-y-6">
      <section>
        <DashboardMetrics metrics={metrics ?? { todayPatients: 0, pendingTreatments: 0, outstandingInvoices: 0 }} loading={loading} />
      </section>

      <section>
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <QuickActions
              onCreatePatient={() => router.push('/dashboard/patients?create=1')}
              onSearchPatients={() => router.push('/dashboard/patients')}
              onViewPendingTreatments={() => router.push('/dashboard/patients?tab=pending-treatments')}
              onProcessPayments={() => router.push('/dashboard/billing')}
            />
          </CardContent>
        </Card>
      </section>

      <section>
        <RecentActivity activities={activities} loading={loading} />
      </section>

      {error ? (
        <Card>
          <CardContent className="flex items-center justify-between gap-4 py-4">
            <div className="text-sm text-destructive">{error}</div>
            <Button size="sm" onClick={loadData}>Retry</Button>
          </CardContent>
        </Card>
      ) : null}
      <Toaster richColors closeButton />
    </div>
  )
}
