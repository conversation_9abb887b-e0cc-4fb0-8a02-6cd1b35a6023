import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import { NextRequest } from "next/server";
import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

// Helper to create a mock request
function createMockRequest(
  method: string,
  url: string,
  body?: any,
  headers?: Record<string, string>
): NextRequest {
  return new NextRequest(url, {
    method,
    headers: {
      "Content-Type": "application/json",
      ...headers,
    },
    body: body ? JSON.stringify(body) : undefined,
  });
}

describe("Tenant Context and Session Management E2E Tests", () => {
  let tenant1: any;
  let tenant2: any;
  let user1: any;
  let user2: any;
  const testPassword = "SecurePass123!";

  beforeEach(async () => {
    // Clean up database before each test
    await prisma.user.deleteMany();
    await prisma.tenant.deleteMany();

    // Create two test tenants
    tenant1 = await prisma.tenant.create({
      data: {
        id: "tenant-1",
        name: "<PERSON> One",
        address: "123 First St, City, State 12345",
        phoneNumber: "******-111-1111",
      },
    });

    tenant2 = await prisma.tenant.create({
      data: {
        id: "tenant-2",
        name: "Clinic Two",
        address: "456 Second St, City, State 12345",
        phoneNumber: "******-222-2222",
      },
    });

    // Create users for each tenant
    const hashedPassword = await bcrypt.hash(testPassword, 12);
    
    user1 = await prisma.user.create({
      data: {
        tenantId: tenant1.id,
        username: "user1",
        email: "<EMAIL>",
        firstName: "John",
        lastName: "Doe",
        password: hashedPassword,
        userType: "DENTIST",
        isActive: true,
      },
    });

    user2 = await prisma.user.create({
      data: {
        tenantId: tenant2.id,
        username: "user2",
        email: "<EMAIL>",
        firstName: "Jane",
        lastName: "Smith",
        password: hashedPassword,
        userType: "DENTIST",
        isActive: true,
      },
    });
  });

  afterEach(async () => {
    // Clean up database after each test
    await prisma.user.deleteMany();
    await prisma.tenant.deleteMany();
  });

  describe("Tenant Context Isolation", () => {
    it("should set correct tenant context on login", async () => {
      const loginData = {
        username: "user1",
        password: testPassword,
      };

      const { POST } = await import("@/app/api/auth/login/route");

      const request = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        loginData
      );

      const response = await POST(request);
      const responseData = await response.json();

      // Verify response includes correct tenant information
      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.user.tenantId).toBe(tenant1.id);
      expect(responseData.user.id).toBe(user1.id);

      // Verify session contains tenant context
      const setCookieHeader = response.headers.get("set-cookie");
      expect(setCookieHeader).toBeDefined();
      expect(setCookieHeader).toContain("auth-token");
    });

    it("should maintain separate contexts for different tenants", async () => {
      // Login user from tenant 1
      const login1Data = {
        username: "user1",
        password: testPassword,
      };

      const { POST: loginPOST } = await import("@/app/api/auth/login/route");

      const request1 = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        login1Data
      );

      const response1 = await loginPOST(request1);
      const responseData1 = await response1.json();

      // Login user from tenant 2
      const login2Data = {
        username: "user2",
        password: testPassword,
      };

      const request2 = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        login2Data
      );

      const response2 = await loginPOST(request2);
      const responseData2 = await response2.json();

      // Both logins should succeed with correct tenant isolation
      expect(response1.status).toBe(200);
      expect(response2.status).toBe(200);
      
      expect(responseData1.user.tenantId).toBe(tenant1.id);
      expect(responseData2.user.tenantId).toBe(tenant2.id);
      
      expect(responseData1.user.id).toBe(user1.id);
      expect(responseData2.user.id).toBe(user2.id);

      // Sessions should be different
      const cookie1 = response1.headers.get("set-cookie");
      const cookie2 = response2.headers.get("set-cookie");
      
      expect(cookie1).toBeDefined();
      expect(cookie2).toBeDefined();
      expect(cookie1).not.toBe(cookie2);
    });

    it("should prevent cross-tenant user access", async () => {
      // Try to login with user1's username but from a different context
      // This simulates potential security issues with tenant isolation
      
      const loginData = {
        username: "user1", // This user exists in tenant1
        password: testPassword,
      };

      const { POST } = await import("@/app/api/auth/login/route");

      const request = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        loginData
      );

      const response = await POST(request);
      const responseData = await response.json();

      // Should successfully authenticate and get the correct tenant
      expect(response.status).toBe(200);
      expect(responseData.user.tenantId).toBe(tenant1.id);
      expect(responseData.user.username).toBe("user1");
    });

    it("should handle users with same username in different tenants", async () => {
      // Create users with same username in different tenants
      const hashedPassword = await bcrypt.hash(testPassword, 12);
      
      const duplicateUser1 = await prisma.user.create({
        data: {
          tenantId: tenant1.id,
          username: "sameuser",
          email: "<EMAIL>",
          firstName: "Same",
          lastName: "User1",
          password: hashedPassword,
          userType: "DENTIST",
          isActive: true,
        },
      });

      const duplicateUser2 = await prisma.user.create({
        data: {
          tenantId: tenant2.id,
          username: "sameuser",
          email: "<EMAIL>",
          firstName: "Same",
          lastName: "User2",
          password: hashedPassword,
          userType: "DENTIST",
          isActive: true,
        },
      });

      // Login with the same username - should resolve to the first match or handle appropriately
      const loginData = {
        username: "sameuser",
        password: testPassword,
      };

      const { POST } = await import("@/app/api/auth/login/route");

      const request = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        loginData
      );

      const response = await POST(request);
      const responseData = await response.json();

      // Should authenticate successfully with one of the users
      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.user.username).toBe("sameuser");
      expect([tenant1.id, tenant2.id]).toContain(responseData.user.tenantId);
    });
  });

  describe("Session Management Across Tenants", () => {
    it("should create unique sessions for each tenant", async () => {
      // Create sessions for users from different tenants
      const sessions: string[] = [];

      // Login user1
      const login1Data = { username: "user1", password: testPassword };
      const { POST: loginPOST } = await import("@/app/api/auth/login/route");

      const request1 = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        login1Data
      );

      const response1 = await loginPOST(request1);
      expect(response1.status).toBe(200);

      const cookie1 = response1.headers.get("set-cookie");
      const token1 = cookie1?.match(/auth-token=([^;]+)/)?.[1];
      expect(token1).toBeDefined();
      sessions.push(token1!);

      // Login user2
      const login2Data = { username: "user2", password: testPassword };

      const request2 = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        login2Data
      );

      const response2 = await loginPOST(request2);
      expect(response2.status).toBe(200);

      const cookie2 = response2.headers.get("set-cookie");
      const token2 = cookie2?.match(/auth-token=([^;]+)/)?.[1];
      expect(token2).toBeDefined();
      sessions.push(token2!);

      // Verify sessions are unique
      expect(sessions[0]).not.toBe(sessions[1]);
      expect(sessions.length).toBe(2);
    });

    it("should validate session with correct tenant context", async () => {
      // Login user1 to get a session
      const loginData = { username: "user1", password: testPassword };
      const { POST: loginPOST } = await import("@/app/api/auth/login/route");

      const loginRequest = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        loginData
      );

      const loginResponse = await loginPOST(loginRequest);
      expect(loginResponse.status).toBe(200);

      const setCookieHeader = loginResponse.headers.get("set-cookie");
      const sessionToken = setCookieHeader?.match(/auth-token=([^;]+)/)?.[1];
      expect(sessionToken).toBeDefined();

      // Use the session to make an authenticated request
      // Import and test the auth middleware or a protected endpoint
      const { AuthService } = await import("@/lib/auth/services/auth-service");

      // Validate the session
      const sessionData = await AuthService.validateSession(sessionToken!);

      expect(sessionData).toBeDefined();
      expect(sessionData?.userId).toBe(user1.id.toString());
      expect(sessionData?.tenantId).toBe(tenant1.id);
    });

    it("should properly destroy sessions on logout", async () => {
      // Login to create a session
      const loginData = { username: "user1", password: testPassword };
      const { POST: loginPOST } = await import("@/app/api/auth/login/route");

      const loginRequest = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        loginData
      );

      const loginResponse = await loginPOST(loginRequest);
      expect(loginResponse.status).toBe(200);

      const setCookieHeader = loginResponse.headers.get("set-cookie");
      const sessionToken = setCookieHeader?.match(/auth-token=([^;]+)/)?.[1];
      expect(sessionToken).toBeDefined();

      // Verify session is valid before logout
      const { AuthService } = await import("@/lib/auth/services/auth-service");
      const sessionBefore = await AuthService.validateSession(sessionToken!);
      expect(sessionBefore).toBeDefined();

      // Logout
      const { POST: logoutPOST } = await import("@/app/api/auth/logout/route");

      const logoutRequest = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/logout",
        {},
        { Cookie: `auth-token=${sessionToken}` }
      );

      const logoutResponse = await logoutPOST(logoutRequest);
      const logoutData = await logoutResponse.json();

      expect(logoutResponse.status).toBe(200);
      expect(logoutData.success).toBe(true);

      // Verify session is invalid after logout
      const sessionAfter = await AuthService.validateSession(sessionToken!);
      expect(sessionAfter).toBeNull();
    });
  });

  describe("Multi-Tenant Registration Flow", () => {
    it("should create tenant-specific admin users during registration", async () => {
      const registrationData = {
        clinic: {
          name: "New Test Clinic",
          address: "789 New St, City, State 12345",
          phone: "******-333-3333",
          email: "<EMAIL>",
        },
        adminUser: {
          username: "newadmin",
          email: "<EMAIL>",
          firstName: "New",
          lastName: "Admin",
          password: "NewSecurePass123!",
          confirmPassword: "NewSecurePass123!",
        },
      };

      const { POST } = await import("@/app/api/auth/register/route");

      const request = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/register",
        registrationData
      );

      const response = await POST(request);
      const responseData = await response.json();

      // Verify registration success
      expect(response.status).toBe(201);
      expect(responseData.success).toBe(true);
      expect(responseData.tenantId).toBeDefined();

      // Verify new tenant was created
      const newTenant = await prisma.tenant.findFirst({
        where: { name: registrationData.clinic.name },
      });

      expect(newTenant).toBeDefined();
      expect(newTenant?.id).toBe(responseData.tenantId);

      // Verify admin user was created with correct tenant context
      const adminUser = await prisma.user.findFirst({
        where: {
          tenantId: newTenant?.id,
          username: registrationData.adminUser.username,
        },
      });

      expect(adminUser).toBeDefined();
      expect(adminUser?.userType).toBe("ADMIN");
      expect(adminUser?.tenantId).toBe(newTenant?.id);

      // Verify admin can login and get correct tenant context
      const loginData = {
        username: registrationData.adminUser.username,
        password: registrationData.adminUser.password,
      };

      const { POST: loginPOST } = await import("@/app/api/auth/login/route");

      const loginRequest = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        loginData
      );

      const loginResponse = await loginPOST(loginRequest);
      const loginResponseData = await loginResponse.json();

      expect(loginResponse.status).toBe(200);
      expect(loginResponseData.success).toBe(true);
      expect(loginResponseData.user.tenantId).toBe(newTenant?.id);
      expect(loginResponseData.user.userType).toBe("ADMIN");
    });

    it("should maintain data isolation between tenants after registration", async () => {
      // Register a new clinic
      const registrationData = {
        clinic: {
          name: "Isolated Test Clinic",
          address: "999 Isolated St, City, State 12345",
          phone: "******-999-9999",
          email: "<EMAIL>",
        },
        adminUser: {
          username: "isolatedadmin",
          email: "<EMAIL>",
          firstName: "Isolated",
          lastName: "Admin",
          password: "IsolatedPass123!",
          confirmPassword: "IsolatedPass123!",
        },
      };

      const { POST: registerPOST } = await import("@/app/api/auth/register/route");

      const registerRequest = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/register",
        registrationData
      );

      const registerResponse = await registerPOST(registerRequest);
      const registerData = await registerResponse.json();

      expect(registerResponse.status).toBe(201);
      expect(registerData.success).toBe(true);

      // Login the new admin
      const newAdminLogin = {
        username: "isolatedadmin",
        password: "IsolatedPass123!",
      };

      const { POST: loginPOST } = await import("@/app/api/auth/login/route");

      const newAdminRequest = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        newAdminLogin
      );

      const newAdminResponse = await loginPOST(newAdminRequest);
      const newAdminData = await newAdminResponse.json();

      // Login existing user from different tenant
      const existingUserLogin = {
        username: "user1",
        password: testPassword,
      };

      const existingUserRequest = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        existingUserLogin
      );

      const existingUserResponse = await loginPOST(existingUserRequest);
      const existingUserData = await existingUserResponse.json();

      // Verify both logins succeed with different tenant contexts
      expect(newAdminResponse.status).toBe(200);
      expect(existingUserResponse.status).toBe(200);

      expect(newAdminData.user.tenantId).toBe(registerData.tenantId);
      expect(existingUserData.user.tenantId).toBe(tenant1.id);

      expect(newAdminData.user.tenantId).not.toBe(existingUserData.user.tenantId);
    });
  });

  describe("Error Scenarios with Tenant Context", () => {
    it("should handle tenant context errors gracefully", async () => {
      // Mock a scenario where tenant context creation fails
      const loginData = {
        username: "user1",
        password: testPassword,
      };

      // This test verifies that even if tenant context has issues,
      // the system fails gracefully
      const { POST } = await import("@/app/api/auth/login/route");

      const request = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        loginData
      );

      const response = await POST(request);
      
      // Should either succeed with proper context or fail gracefully
      expect([200, 500]).toContain(response.status);
      
      if (response.status === 200) {
        const responseData = await response.json();
        expect(responseData.user.tenantId).toBe(tenant1.id);
      } else {
        const responseData = await response.json();
        expect(responseData.success).toBe(false);
      }
    });

    it("should handle missing tenant data during authentication", async () => {
      // Delete the tenant after creating the user (simulate orphaned user)
      await prisma.tenant.delete({
        where: { id: tenant1.id },
      });

      const loginData = {
        username: "user1",
        password: testPassword,
      };

      const { POST } = await import("@/app/api/auth/login/route");

      const request = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        loginData
      );

      const response = await POST(request);
      
      // Should handle gracefully (either prevent login or handle missing tenant)
      expect([401, 500]).toContain(response.status);
      
      const responseData = await response.json();
      expect(responseData.success).toBe(false);
    });
  });
});
