import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import { NextRequest } from "next/server";
import { AuthMiddleware, RoleGuard } from "@/lib/auth/services";
import { AuthService } from "@/lib/auth/services/auth-service";
import { TenantContextManager } from "@/lib/tenant-context";
import { UserType } from "@/lib/auth/types";

// Mock AuthService
vi.mock("@/lib/auth/services/auth-service", () => ({
  AuthService: {
    validateSession: vi.fn(),
  },
}));

// Mock TenantContextManager
const mockTenantContextManager = {
  createContextFromAuthSession: vi.fn(),
  setGlobalContext: vi.fn(),
  clearGlobalContext: vi.fn(),
  getCurrentContext: vi.fn(),
  getCurrentTenantId: vi.fn(),
};

vi.mock("@/lib/tenant-context", () => ({
  TenantContextManager: mockTenantContextManager,
}));

const mockAuthService = AuthService as any;

describe("Authentication Middleware Integration", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe("AuthMiddleware.createApiMiddleware", () => {
    it("should allow access for valid authenticated user", async () => {
      // Setup mocks
      const mockSession = {
        userId: "user-123",
        tenantId: "tenant-456",
        username: "testuser",
        userType: "DENTIST" as UserType,
        isActive: true,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 86400000), // 24 hours from now
      };

      const mockTenantContext = {
        tenantId: "tenant-456",
        userId: "user-123",
        userRole: "DENTIST" as UserType,
        isAuthenticated: true,
      };

      mockAuthService.validateSession.mockResolvedValue({
        isValid: true,
        session: mockSession,
        message: "Session is valid",
      });

      mockTenantContextManager.createContextFromAuthSession.mockReturnValue(mockTenantContext);

      // Create middleware
      const middleware = AuthMiddleware.createApiMiddleware({
        requireAuth: true,
        allowedUserTypes: ["DENTIST", "ADMIN"],
        requireTenant: true,
      });

      // Create mock request
      const request = new NextRequest("http://localhost:3000/api/test");

      // Execute middleware
      const result = await middleware(request);

      expect(result.isAuthorized).toBe(true);
      expect(result.authContext).toBeDefined();
      expect(result.authContext?.user?.userType).toBe("DENTIST");
      expect(result.authContext?.tenant?.id).toBe("tenant-456");
      expect(mockTenantContextManager.setGlobalContext).toHaveBeenCalledWith(mockTenantContext);
    });

    it("should deny access for unauthenticated user when auth required", async () => {
      // Setup mocks
      mockAuthService.validateSession.mockResolvedValue({
        isValid: false,
        message: "No session token found",
      });

      // Create middleware
      const middleware = AuthMiddleware.createApiMiddleware({
        requireAuth: true,
      });

      // Create mock request
      const request = new NextRequest("http://localhost:3000/api/test");

      // Execute middleware
      const result = await middleware(request);

      expect(result.isAuthorized).toBe(false);
      expect(result.response).toBeDefined();
      expect(result.authContext).toBeUndefined();
      
      // Check response
      const responseData = await result.response!.json();
      expect(responseData.success).toBe(false);
      expect(responseData.message).toBe("Authentication required");
    });

    it("should allow access for unauthenticated user when auth not required", async () => {
      // Setup mocks
      mockAuthService.validateSession.mockResolvedValue({
        isValid: false,
        message: "No session token found",
      });

      // Create middleware
      const middleware = AuthMiddleware.createApiMiddleware({
        requireAuth: false,
      });

      // Create mock request
      const request = new NextRequest("http://localhost:3000/api/test");

      // Execute middleware
      const result = await middleware(request);

      expect(result.isAuthorized).toBe(true);
      expect(result.authContext).toBeUndefined();
    });

    it("should deny access for wrong user type", async () => {
      // Setup mocks
      const mockSession = {
        userId: "user-123",
        tenantId: "tenant-456",
        username: "testuser",
        userType: "RECEPTIONIST" as UserType,
        isActive: true,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 86400000),
      };

      mockAuthService.validateSession.mockResolvedValue({
        isValid: true,
        session: mockSession,
        message: "Session is valid",
      });

      // Create middleware
      const middleware = AuthMiddleware.createApiMiddleware({
        requireAuth: true,
        allowedUserTypes: ["DENTIST", "ADMIN"],
      });

      // Create mock request
      const request = new NextRequest("http://localhost:3000/api/test");

      // Execute middleware
      const result = await middleware(request);

      expect(result.isAuthorized).toBe(false);
      expect(result.response).toBeDefined();
      
      // Check response
      const responseData = await result.response!.json();
      expect(responseData.success).toBe(false);
      expect(responseData.message).toBe("Insufficient permissions");
    });

    it("should deny access when tenant required but missing", async () => {
      // Setup mocks
      const mockSession = {
        userId: "user-123",
        tenantId: "", // Empty tenant ID
        username: "testuser",
        userType: "DENTIST" as UserType,
        isActive: true,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 86400000),
      };

      mockAuthService.validateSession.mockResolvedValue({
        isValid: true,
        session: mockSession,
        message: "Session is valid",
      });

      // Create middleware
      const middleware = AuthMiddleware.createApiMiddleware({
        requireAuth: true,
        requireTenant: true,
      });

      // Create mock request
      const request = new NextRequest("http://localhost:3000/api/test");

      // Execute middleware
      const result = await middleware(request);

      expect(result.isAuthorized).toBe(false);
      expect(result.response).toBeDefined();
      
      // Check response
      const responseData = await result.response!.json();
      expect(responseData.success).toBe(false);
      expect(responseData.message).toBe("Tenant context required");
    });

    it("should handle middleware errors gracefully", async () => {
      // Setup mocks to throw error
      mockAuthService.validateSession.mockRejectedValue(new Error("Database connection failed"));

      // Create middleware
      const middleware = AuthMiddleware.createApiMiddleware();

      // Create mock request
      const request = new NextRequest("http://localhost:3000/api/test");

      // Execute middleware
      const result = await middleware(request);

      expect(result.isAuthorized).toBe(false);
      expect(result.response).toBeDefined();
      
      // Check response
      const responseData = await result.response!.json();
      expect(responseData.success).toBe(false);
      expect(responseData.message).toBe("Authentication error");
    });
  });

  describe("AuthMiddleware.withAuth", () => {
    it("should call handler with auth context for authorized user", async () => {
      // Setup mocks
      const mockSession = {
        userId: "user-123",
        tenantId: "tenant-456",
        username: "testuser",
        userType: "ADMIN" as UserType,
        isActive: true,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 86400000),
      };

      mockAuthService.validateSession.mockResolvedValue({
        isValid: true,
        session: mockSession,
        message: "Session is valid",
      });

      mockTenantContextManager.createContextFromAuthSession.mockReturnValue({
        tenantId: "tenant-456",
        userId: "user-123",
        userRole: "ADMIN",
        isAuthenticated: true,
      });

      // Create mock handler
      const mockHandler = vi.fn().mockResolvedValue(
        Response.json({ success: true, data: "test" })
      );

      // Create wrapped handler
      const wrappedHandler = AuthMiddleware.withAuth(mockHandler, {
        requireAuth: true,
        allowedUserTypes: ["ADMIN"],
      });

      // Create mock request
      const request = new NextRequest("http://localhost:3000/api/test");

      // Execute wrapped handler
      const response = await wrappedHandler(request);

      expect(mockHandler).toHaveBeenCalledWith(
        request,
        expect.objectContaining({
          authContext: expect.objectContaining({
            user: expect.objectContaining({
              userType: "ADMIN",
              tenantId: "tenant-456",
            }),
          }),
        })
      );

      const responseData = await response.json();
      expect(responseData.success).toBe(true);
      expect(responseData.data).toBe("test");
    });

    it("should return unauthorized response for unauthorized user", async () => {
      // Setup mocks
      mockAuthService.validateSession.mockResolvedValue({
        isValid: false,
        message: "No session token found",
      });

      // Create mock handler
      const mockHandler = vi.fn();

      // Create wrapped handler
      const wrappedHandler = AuthMiddleware.withAuth(mockHandler, {
        requireAuth: true,
      });

      // Create mock request
      const request = new NextRequest("http://localhost:3000/api/test");

      // Execute wrapped handler
      const response = await wrappedHandler(request);

      expect(mockHandler).not.toHaveBeenCalled();
      
      const responseData = await response.json();
      expect(responseData.success).toBe(false);
      expect(responseData.message).toBe("Authentication required");
    });
  });

  describe("AuthMiddleware.checkRouteAccess", () => {
    it("should return authorized for valid session", async () => {
      // Setup mocks
      const mockSession = {
        userId: "user-123",
        tenantId: "tenant-456",
        username: "testuser",
        userType: "DENTIST" as UserType,
        isActive: true,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 86400000),
      };

      mockAuthService.validateSession.mockResolvedValue({
        isValid: true,
        session: mockSession,
        message: "Session is valid",
      });

      // Execute route access check
      const result = await AuthMiddleware.checkRouteAccess({
        allowedUserTypes: ["DENTIST", "ADMIN"],
      });

      expect(result.isAuthorized).toBe(true);
      expect(result.authContext).toBeDefined();
      expect(result.authContext?.user?.userType).toBe("DENTIST");
      expect(result.redirectUrl).toBeUndefined();
    });

    it("should return unauthorized with login redirect for invalid session", async () => {
      // Setup mocks
      mockAuthService.validateSession.mockResolvedValue({
        isValid: false,
        message: "Session expired",
      });

      // Execute route access check
      const result = await AuthMiddleware.checkRouteAccess();

      expect(result.isAuthorized).toBe(false);
      expect(result.authContext).toBeUndefined();
      expect(result.redirectUrl).toBe("/auth/login");
    });

    it("should return unauthorized with unauthorized redirect for insufficient privileges", async () => {
      // Setup mocks
      const mockSession = {
        userId: "user-123",
        tenantId: "tenant-456",
        username: "testuser",
        userType: "RECEPTIONIST" as UserType,
        isActive: true,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 86400000),
      };

      mockAuthService.validateSession.mockResolvedValue({
        isValid: true,
        session: mockSession,
        message: "Session is valid",
      });

      // Execute route access check
      const result = await AuthMiddleware.checkRouteAccess({
        allowedUserTypes: ["ADMIN", "DENTIST"],
      });

      expect(result.isAuthorized).toBe(false);
      expect(result.authContext).toBeUndefined();
      expect(result.redirectUrl).toBe("/unauthorized");
    });
  });
});

describe("RoleGuard", () => {
  describe("hasPermission", () => {
    it("should allow higher privilege user to access lower privilege operation", () => {
      expect(RoleGuard.hasPermission("SUPERUSER", "ADMIN")).toBe(true);
      expect(RoleGuard.hasPermission("ADMIN", "DENTIST")).toBe(true);
      expect(RoleGuard.hasPermission("DENTIST", "HYGIENIST")).toBe(true);
      expect(RoleGuard.hasPermission("HYGIENIST", "ASSISTANT")).toBe(true);
      expect(RoleGuard.hasPermission("ASSISTANT", "RECEPTIONIST")).toBe(true);
    });

    it("should allow same privilege level", () => {
      expect(RoleGuard.hasPermission("ADMIN", "ADMIN")).toBe(true);
      expect(RoleGuard.hasPermission("DENTIST", "DENTIST")).toBe(true);
      expect(RoleGuard.hasPermission("RECEPTIONIST", "RECEPTIONIST")).toBe(true);
    });

    it("should deny lower privilege user from accessing higher privilege operation", () => {
      expect(RoleGuard.hasPermission("ADMIN", "SUPERUSER")).toBe(false);
      expect(RoleGuard.hasPermission("DENTIST", "ADMIN")).toBe(false);
      expect(RoleGuard.hasPermission("RECEPTIONIST", "HYGIENIST")).toBe(false);
    });
  });

  describe("isAdmin", () => {
    it("should return true for admin users", () => {
      expect(RoleGuard.isAdmin("SUPERUSER")).toBe(true);
      expect(RoleGuard.isAdmin("ADMIN")).toBe(true);
    });

    it("should return false for non-admin users", () => {
      expect(RoleGuard.isAdmin("DENTIST")).toBe(false);
      expect(RoleGuard.isAdmin("HYGIENIST")).toBe(false);
      expect(RoleGuard.isAdmin("ASSISTANT")).toBe(false);
      expect(RoleGuard.isAdmin("RECEPTIONIST")).toBe(false);
    });
  });

  describe("isClinicalStaff", () => {
    it("should return true for clinical staff", () => {
      expect(RoleGuard.isClinicalStaff("SUPERUSER")).toBe(true);
      expect(RoleGuard.isClinicalStaff("ADMIN")).toBe(true);
      expect(RoleGuard.isClinicalStaff("DENTIST")).toBe(true);
      expect(RoleGuard.isClinicalStaff("HYGIENIST")).toBe(true);
    });

    it("should return false for non-clinical staff", () => {
      expect(RoleGuard.isClinicalStaff("ASSISTANT")).toBe(false);
      expect(RoleGuard.isClinicalStaff("RECEPTIONIST")).toBe(false);
    });
  });

  describe("canManageUsers", () => {
    it("should return true for users who can manage other users", () => {
      expect(RoleGuard.canManageUsers("SUPERUSER")).toBe(true);
      expect(RoleGuard.canManageUsers("ADMIN")).toBe(true);
    });

    it("should return false for users who cannot manage other users", () => {
      expect(RoleGuard.canManageUsers("DENTIST")).toBe(false);
      expect(RoleGuard.canManageUsers("HYGIENIST")).toBe(false);
      expect(RoleGuard.canManageUsers("ASSISTANT")).toBe(false);
      expect(RoleGuard.canManageUsers("RECEPTIONIST")).toBe(false);
    });
  });

  describe("middleware options creators", () => {
    it("should create correct admin-only options", () => {
      const options = RoleGuard.adminOnly();
      expect(options).toEqual({
        requireAuth: true,
        allowedUserTypes: ["SUPERUSER", "ADMIN"],
        requireTenant: true,
      });
    });

    it("should create correct clinical staff options", () => {
      const options = RoleGuard.clinicalStaffOnly();
      expect(options).toEqual({
        requireAuth: true,
        allowedUserTypes: ["SUPERUSER", "ADMIN", "DENTIST", "HYGIENIST"],
        requireTenant: true,
      });
    });

    it("should create correct authenticated-only options", () => {
      const options = RoleGuard.authenticatedOnly();
      expect(options).toEqual({
        requireAuth: true,
        allowedUserTypes: ["SUPERUSER", "ADMIN", "DENTIST", "HYGIENIST", "ASSISTANT", "RECEPTIONIST"],
        requireTenant: true,
      });
    });
  });
});
