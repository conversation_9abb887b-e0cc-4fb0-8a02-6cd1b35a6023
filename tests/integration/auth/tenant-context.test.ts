import { describe, it, expect, beforeEach, afterEach } from "vitest";
import { TenantContextManager } from "@/lib/tenant-context";
import { AuthService } from "@/lib/auth/services";
import { UserType } from "@/lib/auth/types";

describe("Tenant Context Integration", () => {
  beforeEach(() => {
    // Clear any existing context before each test
    TenantContextManager.clearGlobalContext();
  });

  afterEach(() => {
    // Clean up after each test
    TenantContextManager.clearGlobalContext();
  });

  describe("TenantContextManager Authentication Integration", () => {
    it("should create context from auth session", () => {
      const sessionInfo = {
        userId: "user-123",
        tenantId: "tenant-456",
        username: "testuser",
        userType: "DENTIST" as UserType,
        isActive: true,
      };

      const context = TenantContextManager.createContextFromAuthSession(
        sessionInfo,
        "session-token-789",
        "Test Clinic"
      );

      expect(context).toEqual({
        tenantId: "tenant-456",
        tenantName: "Test Clinic",
        userId: "user-123",
        userRole: "DENTIST",
        sessionId: "session-token-789",
        isAuthenticated: true,
      });
    });

    it("should handle session without optional fields", () => {
      const sessionInfo = {
        userId: "user-123",
        tenantId: "tenant-456",
        username: "testuser",
        userType: "ADMIN" as UserType,
        isActive: true,
      };

      const context = TenantContextManager.createContextFromAuthSession(sessionInfo);

      expect(context).toEqual({
        tenantId: "tenant-456",
        tenantName: undefined,
        userId: "user-123",
        userRole: "ADMIN",
        sessionId: undefined,
        isAuthenticated: true,
      });
    });

    it("should handle inactive user session", () => {
      const sessionInfo = {
        userId: "user-123",
        tenantId: "tenant-456",
        username: "testuser",
        userType: "RECEPTIONIST" as UserType,
        isActive: false,
      };

      const context = TenantContextManager.createContextFromAuthSession(sessionInfo);

      expect(context.isAuthenticated).toBe(false);
    });
  });

  describe("Context Storage and Retrieval", () => {
    it("should set and get global context", () => {
      const testContext = {
        tenantId: "tenant-123",
        tenantName: "Test Clinic",
        userId: "user-456",
        userRole: "DENTIST" as UserType,
        sessionId: "session-789",
        isAuthenticated: true,
      };

      TenantContextManager.setGlobalContext(testContext);
      const retrievedContext = TenantContextManager.getCurrentContext();

      expect(retrievedContext).toEqual(testContext);
    });

    it("should return tenant ID from context", () => {
      const testContext = {
        tenantId: "tenant-abc",
        userId: "user-def",
        userRole: "ADMIN" as UserType,
        isAuthenticated: true,
      };

      TenantContextManager.setGlobalContext(testContext);
      const tenantId = TenantContextManager.getCurrentTenantId();

      expect(tenantId).toBe("tenant-abc");
    });

    it("should return null when no context is set", () => {
      const context = TenantContextManager.getCurrentContext();
      const tenantId = TenantContextManager.getCurrentTenantId();

      expect(context).toBeNull();
      expect(tenantId).toBeNull();
    });

    it("should clear global context", () => {
      const testContext = {
        tenantId: "tenant-123",
        userId: "user-456",
        userRole: "HYGIENIST" as UserType,
        isAuthenticated: true,
      };

      TenantContextManager.setGlobalContext(testContext);
      TenantContextManager.clearGlobalContext();
      
      const context = TenantContextManager.getCurrentContext();
      expect(context).toBeNull();
    });
  });

  describe("Async Context Management", () => {
    it("should run function with specific context", async () => {
      const testContext = {
        tenantId: "tenant-async",
        userId: "user-async",
        userRole: "SUPERUSER" as UserType,
        isAuthenticated: true,
      };

      const result = await TenantContextManager.runWithContext(testContext, async () => {
        const currentContext = TenantContextManager.getCurrentContext();
        expect(currentContext).toEqual(testContext);
        return "test-result";
      });

      expect(result).toBe("test-result");
      
      // Context should not leak outside the function
      const outsideContext = TenantContextManager.getCurrentContext();
      expect(outsideContext).toBeNull();
    });

    it("should run synchronous function with context", () => {
      const testContext = {
        tenantId: "tenant-sync",
        userId: "user-sync",
        userRole: "ASSISTANT" as UserType,
        isAuthenticated: true,
      };

      const result = TenantContextManager.runWithContextSync(testContext, () => {
        const currentContext = TenantContextManager.getCurrentContext();
        expect(currentContext).toEqual(testContext);
        return 42;
      });

      expect(result).toBe(42);
      
      // Context should not leak outside the function
      const outsideContext = TenantContextManager.getCurrentContext();
      expect(outsideContext).toBeNull();
    });

    it("should handle nested context calls", async () => {
      const outerContext = {
        tenantId: "tenant-outer",
        userId: "user-outer",
        userRole: "ADMIN" as UserType,
        isAuthenticated: true,
      };

      const innerContext = {
        tenantId: "tenant-inner",
        userId: "user-inner",
        userRole: "DENTIST" as UserType,
        isAuthenticated: true,
      };

      await TenantContextManager.runWithContext(outerContext, async () => {
        const currentOuter = TenantContextManager.getCurrentContext();
        expect(currentOuter).toEqual(outerContext);

        await TenantContextManager.runWithContext(innerContext, async () => {
          const currentInner = TenantContextManager.getCurrentContext();
          expect(currentInner).toEqual(innerContext);
        });

        // Should restore outer context
        const restoredOuter = TenantContextManager.getCurrentContext();
        expect(restoredOuter).toEqual(outerContext);
      });
    });
  });

  describe("JWT Integration", () => {
    it("should create context from JWT token", () => {
      // Create a mock JWT payload
      const payload = {
        tenantId: "tenant-jwt",
        tenantName: "JWT Clinic",
        userId: "user-jwt",
        role: "DENTIST",
      };

      // Create a mock JWT token (base64 encoded JSON for the payload part)
      const header = btoa(JSON.stringify({ alg: "HS256", typ: "JWT" }));
      const payloadEncoded = btoa(JSON.stringify(payload));
      const signature = "mock-signature";
      const token = `${header}.${payloadEncoded}.${signature}`;

      const context = TenantContextManager.createContextFromJWT(token);

      expect(context).toEqual({
        tenantId: "tenant-jwt",
        tenantName: "JWT Clinic",
        userId: "user-jwt",
        userRole: "DENTIST",
      });
    });

    it("should throw error for invalid JWT token", () => {
      const invalidToken = "invalid.token";

      expect(() => {
        TenantContextManager.createContextFromJWT(invalidToken);
      }).toThrow("Invalid JWT token");
    });
  });

  describe("Tenant Access Validation", () => {
    it("should validate tenant access (placeholder implementation)", async () => {
      const hasAccess = await TenantContextManager.validateTenantAccess(
        "user-123",
        "tenant-456"
      );

      // Currently returns true for all cases (placeholder)
      expect(hasAccess).toBe(true);
    });

    it("should get tenant info (placeholder implementation)", async () => {
      const tenantInfo = await TenantContextManager.getTenantInfo("tenant-123");

      expect(tenantInfo).toEqual({
        id: "tenant-123",
        name: "Tenant tenant-123",
        status: "active",
      });
    });
  });

  describe("Backward Compatibility", () => {
    it("should maintain backward compatibility with TenantContext export", async () => {
      const { TenantContext } = await import("@/lib/tenant-context");

      // Test backward compatible methods
      expect(typeof TenantContext.getCurrentTenantId).toBe("function");
      expect(typeof TenantContext.setCurrentTenantId).toBe("function");
      expect(typeof TenantContext.clearTenantId).toBe("function");

      // Test setting via backward compatible method
      TenantContext.setCurrentTenantId("tenant-backwards");
      expect(TenantContext.getCurrentTenantId()).toBe("tenant-backwards");

      // Test clearing via backward compatible method
      TenantContext.clearTenantId();
      expect(TenantContext.getCurrentTenantId()).toBeNull();
    });
  });
});
