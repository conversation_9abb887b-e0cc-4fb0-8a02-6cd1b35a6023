import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import { NextRequest } from "next/server";
import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

// Helper to create a mock request
function createMockRequest(
  method: string,
  url: string,
  body?: any,
  headers?: Record<string, string>
): NextRequest {
  return new NextRequest(url, {
    method,
    headers: {
      "Content-Type": "application/json",
      ...headers,
    },
    body: body ? JSON.stringify(body) : undefined,
  });
}

describe("Login and Logout Flow E2E Tests", () => {
  let testTenant: any;
  let testUser: any;
  const testPassword = "SecurePass123!";

  beforeEach(async () => {
    // Clean up database before each test
    await prisma.user.deleteMany();
    await prisma.tenant.deleteMany();

    // Create test tenant
    testTenant = await prisma.tenant.create({
      data: {
        id: "test-tenant-id",
        name: "Test Dental Clinic",
        address: "123 Main St, City, State 12345",
        phoneNumber: "******-123-4567",
      },
    });

    // Create test user with hashed password
    const hashedPassword = await bcrypt.hash(testPassword, 12);
    testUser = await prisma.user.create({
      data: {
        tenantId: testTenant.id,
        username: "testuser",
        email: "<EMAIL>",
        firstName: "John",
        lastName: "Doe",
        password: hashedPassword,
        userType: "DENTIST",
        isActive: true,
      },
    });
  });

  afterEach(async () => {
    // Clean up database after each test
    await prisma.user.deleteMany();
    await prisma.tenant.deleteMany();
  });

  describe("Login Flow", () => {
    it("should successfully authenticate user with valid credentials", async () => {
      const loginData = {
        username: "testuser",
        password: testPassword,
      };

      const { POST } = await import("@/app/api/auth/login/route");

      const request = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        loginData
      );

      const response = await POST(request);
      const responseData = await response.json();

      // Verify response
      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.user).toBeDefined();
      expect(responseData.user.id).toBe(testUser.id);
      expect(responseData.user.username).toBe(testUser.username);
      expect(responseData.user.tenantId).toBe(testTenant.id);
      expect(responseData.redirectUrl).toBeDefined();

      // Verify session cookie is set
      const setCookieHeader = response.headers.get("set-cookie");
      expect(setCookieHeader).toBeDefined();
      expect(setCookieHeader).toContain("auth-token");
      expect(setCookieHeader).toContain("HttpOnly");
      expect(setCookieHeader).toContain("Secure");
      expect(setCookieHeader).toContain("SameSite=Strict");
    });

    it("should reject login with invalid username", async () => {
      const loginData = {
        username: "nonexistentuser",
        password: testPassword,
      };

      const { POST } = await import("@/app/api/auth/login/route");

      const request = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        loginData
      );

      const response = await POST(request);
      const responseData = await response.json();

      // Verify response
      expect(response.status).toBe(401);
      expect(responseData.success).toBe(false);
      expect(responseData.message).toContain("Invalid credentials");

      // Verify no session cookie is set
      const setCookieHeader = response.headers.get("set-cookie");
      expect(setCookieHeader).toBeNull();
    });

    it("should reject login with invalid password", async () => {
      const loginData = {
        username: "testuser",
        password: "wrongpassword",
      };

      const { POST } = await import("@/app/api/auth/login/route");

      const request = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        loginData
      );

      const response = await POST(request);
      const responseData = await response.json();

      // Verify response
      expect(response.status).toBe(401);
      expect(responseData.success).toBe(false);
      expect(responseData.message).toContain("Invalid credentials");

      // Verify no session cookie is set
      const setCookieHeader = response.headers.get("set-cookie");
      expect(setCookieHeader).toBeNull();
    });

    it("should reject login for inactive user", async () => {
      // Deactivate the test user
      await prisma.user.update({
        where: { id: testUser.id },
        data: { isActive: false },
      });

      const loginData = {
        username: "testuser",
        password: testPassword,
      };

      const { POST } = await import("@/app/api/auth/login/route");

      const request = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        loginData
      );

      const response = await POST(request);
      const responseData = await response.json();

      // Verify response
      expect(response.status).toBe(401);
      expect(responseData.success).toBe(false);
      expect(responseData.message).toContain("account is disabled");
    });

    it("should validate login request data", async () => {
      const invalidData = {
        username: "", // Empty username
        password: "", // Empty password
      };

      const { POST } = await import("@/app/api/auth/login/route");

      const request = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        invalidData
      );

      const response = await POST(request);
      const responseData = await response.json();

      // Verify response
      expect(response.status).toBe(400);
      expect(responseData.success).toBe(false);
      expect(responseData.errors).toBeDefined();
    });

    it("should handle missing request body", async () => {
      const { POST } = await import("@/app/api/auth/login/route");

      const request = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login"
      );

      const response = await POST(request);
      const responseData = await response.json();

      // Verify response
      expect(response.status).toBe(400);
      expect(responseData.success).toBe(false);
    });

    it("should set tenant context on successful login", async () => {
      const loginData = {
        username: "testuser",
        password: testPassword,
      };

      const { POST } = await import("@/app/api/auth/login/route");

      const request = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        loginData
      );

      const response = await POST(request);
      const responseData = await response.json();

      // Verify response includes tenant information
      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.user.tenantId).toBe(testTenant.id);
    });

    it("should redirect to appropriate dashboard based on user type", async () => {
      // Test with ADMIN user
      const adminUser = await prisma.user.create({
        data: {
          tenantId: testTenant.id,
          username: "admin",
          email: "<EMAIL>",
          firstName: "Admin",
          lastName: "User",
          password: await bcrypt.hash(testPassword, 12),
          userType: "ADMIN",
          isActive: true,
        },
      });

      const loginData = {
        username: "admin",
        password: testPassword,
      };

      const { POST } = await import("@/app/api/auth/login/route");

      const request = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        loginData
      );

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.redirectUrl).toBeDefined();
      expect(responseData.user.userType).toBe("ADMIN");
    });
  });

  describe("Rate Limiting", () => {
    it("should implement rate limiting for failed login attempts", async () => {
      const loginData = {
        username: "testuser",
        password: "wrongpassword",
      };

      const { POST } = await import("@/app/api/auth/login/route");

      // Make multiple failed login attempts
      const maxAttempts = 5;
      const responses: Response[] = [];

      for (let i = 0; i < maxAttempts + 1; i++) {
        const request = createMockRequest(
          "POST",
          "http://localhost:3000/api/auth/login",
          loginData
        );

        const response = await POST(request);
        responses.push(response);
      }

      // First few attempts should return 401 (invalid credentials)
      for (let i = 0; i < maxAttempts; i++) {
        expect(responses[i].status).toBe(401);
      }

      // After max attempts, should return 429 (rate limited)
      const lastResponse = responses[maxAttempts];
      if (lastResponse.status === 429) {
        const responseData = await lastResponse.json();
        expect(responseData.success).toBe(false);
        expect(responseData.message).toContain("rate limit");
      }
    });
  });

  describe("Logout Flow", () => {
    it("should successfully logout authenticated user", async () => {
      // First, login to get a session
      const loginData = {
        username: "testuser",
        password: testPassword,
      };

      const { POST: loginPOST } = await import("@/app/api/auth/login/route");

      const loginRequest = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        loginData
      );

      const loginResponse = await loginPOST(loginRequest);
      expect(loginResponse.status).toBe(200);

      // Extract session token from login response
      const setCookieHeader = loginResponse.headers.get("set-cookie");
      expect(setCookieHeader).toBeDefined();

      const sessionToken = setCookieHeader?.match(/auth-token=([^;]+)/)?.[1];
      expect(sessionToken).toBeDefined();

      // Now logout
      const { POST: logoutPOST } = await import("@/app/api/auth/logout/route");

      const logoutRequest = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/logout",
        {},
        {
          Cookie: `auth-token=${sessionToken}`,
        }
      );

      const logoutResponse = await logoutPOST(logoutRequest);
      const logoutData = await logoutResponse.json();

      // Verify logout response
      expect(logoutResponse.status).toBe(200);
      expect(logoutData.success).toBe(true);

      // Verify session cookie is cleared
      const logoutSetCookie = logoutResponse.headers.get("set-cookie");
      expect(logoutSetCookie).toBeDefined();
      expect(logoutSetCookie).toContain("auth-token=");
      expect(logoutSetCookie).toContain("Max-Age=0");
    });

    it("should handle logout without valid session", async () => {
      const { POST } = await import("@/app/api/auth/logout/route");

      const request = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/logout",
        {}
      );

      const response = await POST(request);
      const responseData = await response.json();

      // Should still return success even without valid session
      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
    });
  });

  describe("Session Management", () => {
    it("should create secure session on successful login", async () => {
      const loginData = {
        username: "testuser",
        password: testPassword,
      };

      const { POST } = await import("@/app/api/auth/login/route");

      const request = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        loginData
      );

      const response = await POST(request);
      expect(response.status).toBe(200);

      // Verify session cookie security attributes
      const setCookieHeader = response.headers.get("set-cookie");
      expect(setCookieHeader).toBeDefined();
      expect(setCookieHeader).toContain("HttpOnly");
      expect(setCookieHeader).toContain("Secure");
      expect(setCookieHeader).toContain("SameSite=Strict");
    });

    it("should validate session expiration", async () => {
      // This test would require mocking time to test session expiration
      // For now, we'll verify that the session has an expiration time
      const loginData = {
        username: "testuser",
        password: testPassword,
      };

      const { POST } = await import("@/app/api/auth/login/route");

      const request = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        loginData
      );

      const response = await POST(request);
      expect(response.status).toBe(200);

      // Verify session cookie has Max-Age or Expires
      const setCookieHeader = response.headers.get("set-cookie");
      expect(setCookieHeader).toBeDefined();
      expect(
        setCookieHeader?.includes("Max-Age") || setCookieHeader?.includes("Expires")
      ).toBe(true);
    });
  });

  describe("Error Handling", () => {
    it("should handle database connection errors during login", async () => {
      // Mock Prisma to throw an error
      const originalFindFirst = prisma.user.findFirst;
      prisma.user.findFirst = vi.fn().mockRejectedValue(new Error("Database connection failed"));

      const loginData = {
        username: "testuser",
        password: testPassword,
      };

      const { POST } = await import("@/app/api/auth/login/route");

      const request = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        loginData
      );

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(500);
      expect(responseData.success).toBe(false);
      expect(responseData.message).toContain("error");

      // Restore original method
      prisma.user.findFirst = originalFindFirst;
    });

    it("should handle malformed request data", async () => {
      const { POST } = await import("@/app/api/auth/login/route");

      const request = new NextRequest("http://localhost:3000/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: "invalid-json",
      });

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(400);
      expect(responseData.success).toBe(false);
    });
  });

  describe("Security", () => {
    it("should not reveal whether username or password is incorrect", async () => {
      // Test with non-existent user
      const nonExistentUser = {
        username: "nonexistent",
        password: testPassword,
      };

      // Test with existing user but wrong password
      const wrongPassword = {
        username: "testuser",
        password: "wrongpassword",
      };

      const { POST } = await import("@/app/api/auth/login/route");

      const request1 = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        nonExistentUser
      );

      const request2 = createMockRequest(
        "POST",
        "http://localhost:3000/api/auth/login",
        wrongPassword
      );

      const response1 = await POST(request1);
      const response2 = await POST(request2);

      const data1 = await response1.json();
      const data2 = await response2.json();

      // Both should return the same generic error message
      expect(response1.status).toBe(401);
      expect(response2.status).toBe(401);
      expect(data1.message).toBe(data2.message);
      expect(data1.message).toContain("Invalid credentials");
    });

    it("should use secure password hashing", async () => {
      // Verify that stored password is hashed and not plaintext
      const user = await prisma.user.findFirst({
        where: { username: "testuser" },
      });

      expect(user?.password).toBeDefined();
      expect(user?.password).not.toBe(testPassword);
      expect(user?.password).toMatch(/^\$2[aby]\$\d+\$/); // bcrypt hash pattern
    });
  });
});
