import { describe, it, expect } from 'vitest'
import { PrismaClient, PaymentMethod } from '@prisma/client'
import { applyAllExtensions } from '@/lib/prisma-extensions'

const base = new PrismaClient()
const prisma = applyAllExtensions(base)

async function createTenant() {
  const id = `tenant-${Date.now()}-${Math.random()}`
  return prisma.tenant.create({ data: { id, name: `Tenant ${id}` } })
}

async function createPatient(tenantId: string) {
  return prisma.patient.create({ data: { tenantId, firstName: 'Test', lastName: 'User' } })
}

describe('Integration: Serial generation', () => {
  it('creates case sheet, invoice and payment with serials end-to-end', async () => {
    const t = await createTenant()
    const p = await createPatient(t.id)

    const cs = await (prisma as any).caseSheet.createCaseSheetWithTeeth(p.id, t.id)
    expect(cs.serial).toMatch(/^\d+$/)

    const inv = await (prisma as any).invoice.createInvoice({ tenantId: t.id, patientId: p.id, invoiceDate: new Date(), totalAmount: 200 as any })
    expect(inv.serial).toMatch(/^\d+$/)

    const pay = await (prisma as any).payment.processPayment(p.id, inv.id, 100, PaymentMethod.CASH)
    expect(pay.serial).toMatch(/^\d+$/)
  })
})


