import { describe, it, expect } from 'vitest'
import { NextRequest } from 'next/server'
import prisma from '@/lib/prisma'

import { POST as POST_CREATE_PATIENT } from '@/app/api/patients/create-with-workflow/route'
import { POST as POST_INIT_CASE_SHEET } from '@/app/api/clinical/case-sheets/[id]/initialize/route'
import { POST as POST_CREATE_FINDING } from '@/app/api/clinical/teeth/[id]/findings/route'
import { POST as POST_CREATE_TREATMENT } from '@/app/api/clinical/findings/[id]/treatments/route'
import { POST as POST_RECORD_PAYMENT } from '@/app/api/billing/invoices/[id]/payments/route'

function uniquePhone() { return `${Date.now()}${Math.floor(Math.random()*1000)}` }

async function createTenant(suffix: string = '') {
  return prisma.tenant.create({ data: { name: `E2E Workflow${suffix}` } })
}

async function getCaseSheetWithTeeth(patientId: number) {
  return prisma.caseSheet.findFirst({ where: { patientId }, include: { teeth: true } })
}

async function getInvoiceForPatient(patientId: number) {
  return prisma.invoice.findFirst({ where: { patientId } })
}

describe('End-to-end clinical workflow', () => {
  it('completes patient→case sheet→findings→treatments→invoice→payments workflow with integrity checks', async () => {
    const tenant = await createTenant('-complete')

    // 1) Create patient with complete workflow (case sheet + 32 teeth)
    const body = {
      firstName: 'E2E',
      lastName: 'Patient',
      phoneNumber: uniquePhone(),
      tenantId: tenant.id,
      email: '<EMAIL>',
    }
    const reqCreate = new NextRequest('http://localhost:3000/api/patients/create-with-workflow', {
      method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(body),
    })
    const resCreate = await POST_CREATE_PATIENT(reqCreate)
    const dataCreate = await resCreate.json()

    expect(resCreate.status).toBe(201)
    expect(dataCreate.success).toBe(true)
    const patientId: number = dataCreate.patient.id
    const caseSheetId: number = dataCreate.caseSheet.id
    expect(dataCreate.teethCount).toBe(32)

    // Verify chart created
    const cs = await getCaseSheetWithTeeth(patientId)
    expect(cs?.id).toBe(caseSheetId)
    expect(cs?.teeth.length).toBe(32)

    // 2) Create first finding (no inline treatment) on first tooth
    const toothA = cs!.teeth[0]
    const reqFindingA = new NextRequest(`http://localhost:3000/api/clinical/teeth/${toothA.id}/findings`, {
      method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ description: 'Caries - A' }),
    })
    const resFindingA = await POST_CREATE_FINDING(reqFindingA, { params: { id: String(toothA.id) } })
    const dataFindingA = await resFindingA.json()
    expect(resFindingA.status).toBe(201)
    expect(dataFindingA.success).toBe(true)
    expect(dataFindingA.finding?.id).toBeTruthy()
    expect(dataFindingA.treatment).toBeNull()

    // 3) Add two treatments to first finding and verify invoice aggregation
    const findingAId: number = dataFindingA.finding.id

    const reqTreatA1 = new NextRequest(`http://localhost:3000/api/clinical/findings/${findingAId}/treatments`, {
      method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ procedureName: 'Filling', cost: 200 }),
    })
    const resTreatA1 = await POST_CREATE_TREATMENT(reqTreatA1, { params: { id: String(findingAId) } })
    const dataTreatA1 = await resTreatA1.json()
    expect(resTreatA1.status).toBe(201)
    expect(dataTreatA1.success).toBe(true)
    expect(dataTreatA1.treatment?.id).toBeTruthy()
    expect(dataTreatA1.invoice?.id).toBeTruthy()

    const invoiceId: number = dataTreatA1.invoice.id

    const reqTreatA2 = new NextRequest(`http://localhost:3000/api/clinical/findings/${findingAId}/treatments`, {
      method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ procedureName: 'Onlay', cost: 300 }),
    })
    const resTreatA2 = await POST_CREATE_TREATMENT(reqTreatA2, { params: { id: String(findingAId) } })
    const dataTreatA2 = await resTreatA2.json()
    expect(resTreatA2.status).toBe(201)
    expect(dataTreatA2.success).toBe(true)

    // Verify invoice totals = 200 + 300
    let invoice = await prisma.invoice.findUnique({ where: { id: invoiceId } })
    expect(Number(invoice!.totalAmount)).toBe(500)
    expect(Number(invoice!.amountPaid)).toBe(0)
    expect(Number(invoice!.balanceDue)).toBe(500)

    // 4) Create second finding on a different tooth and add a treatment (aggregates to invoice)
    const toothB = cs!.teeth[1]
    const reqFindingB = new NextRequest(`http://localhost:3000/api/clinical/teeth/${toothB.id}/findings`, {
      method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ description: 'Fracture - B' }),
    })
    const resFindingB = await POST_CREATE_FINDING(reqFindingB, { params: { id: String(toothB.id) } })
    const dataFindingB = await resFindingB.json()
    expect(resFindingB.status).toBe(201)
    const findingBId: number = dataFindingB.finding.id

    const reqTreatB1 = new NextRequest(`http://localhost:3000/api/clinical/findings/${findingBId}/treatments`, {
      method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ procedureName: 'Crown', cost: 150 }),
    })
    const resTreatB1 = await POST_CREATE_TREATMENT(reqTreatB1, { params: { id: String(findingBId) } })
    const dataTreatB1 = await resTreatB1.json()
    expect(resTreatB1.status).toBe(201)
    expect(dataTreatB1.invoice?.id).toBe(invoiceId)

    // Verify invoice totals now = 500 + 150
    invoice = await prisma.invoice.findUnique({ where: { id: invoiceId } })
    expect(Number(invoice!.totalAmount)).toBe(650)
    expect(Number(invoice!.balanceDue)).toBe(650)

    // 5) Record partial and final payments, verify status transitions and balances
    const reqPay1 = new NextRequest(`http://localhost:3000/api/billing/invoices/${invoiceId}/payments`, {
      method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ patientId, amount: 200, paymentMethod: 'CASH' }),
    })
    const resPay1 = await POST_RECORD_PAYMENT(reqPay1, { params: { id: String(invoiceId) } })
    const dataPay1 = await resPay1.json()
    expect(resPay1.status).toBe(201)
    expect(dataPay1.success).toBe(true)

    invoice = await prisma.invoice.findUnique({ where: { id: invoiceId } })
    expect(Number(invoice!.amountPaid)).toBe(200)
    expect(Number(invoice!.balanceDue)).toBe(450)
    // After first payment, status should be SENT (if previously DRAFT)
    expect(["SENT", "PAID"].includes(String(invoice!.status))).toBe(true)

    const reqPay2 = new NextRequest(`http://localhost:3000/api/billing/invoices/${invoiceId}/payments`, {
      method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ patientId, amount: 450, paymentMethod: 'CARD' }),
    })
    const resPay2 = await POST_RECORD_PAYMENT(reqPay2, { params: { id: String(invoiceId) } })
    const dataPay2 = await resPay2.json()
    expect(resPay2.status).toBe(201)
    expect(dataPay2.success).toBe(true)

    invoice = await prisma.invoice.findUnique({ where: { id: invoiceId } })
    expect(Number(invoice!.amountPaid)).toBe(Number(invoice!.totalAmount))
    expect(Number(invoice!.balanceDue)).toBe(0)
    expect(String(invoice!.status)).toBe('PAID')

    // 6) Data integrity: counts and relations
    const findingsCount = await prisma.finding.count({ where: { tooth: { caseSheetId } } })
    expect(findingsCount).toBe(2)
    const treatmentsCount = await prisma.treatment.count({ where: { finding: { tooth: { caseSheetId } } } })
    expect(treatmentsCount).toBe(3)
    const paymentsCount = await prisma.payment.count({ where: { invoiceId } })
    expect(paymentsCount).toBe(2)
  })

  it('enforces validation/rollback: second case sheet init blocked; invalid treatment does not change state', async () => {
    const tenant = await createTenant('-rollback')

    // Create base patient and chart
    const reqCreate = new NextRequest('http://localhost:3000/api/patients/create-with-workflow', {
      method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ firstName: 'RB', lastName: 'Case', phoneNumber: uniquePhone(), tenantId: tenant.id }),
    })
    const resCreate = await POST_CREATE_PATIENT(reqCreate)
    const dataCreate = await resCreate.json()
    const patientId: number = dataCreate.patient.id

    // Attempt to initialize case sheet again for same patient → should be 400, and remain single case sheet
    const reqInit = new NextRequest(`http://localhost:3000/api/clinical/case-sheets/${patientId}/initialize`, { method: 'POST' })
    const resInit = await POST_INIT_CASE_SHEET(reqInit, { params: { id: String(patientId) } })
    expect(resInit.status).toBe(400)

    const csCount = await prisma.caseSheet.count({ where: { patientId } })
    expect(csCount).toBe(1)

    // Seed a finding and valid treatment to establish baseline invoice
    const cs = await getCaseSheetWithTeeth(patientId)
    const toothId = cs!.teeth[0].id
    const resFinding = await POST_CREATE_FINDING(new NextRequest(`http://localhost:3000/api/clinical/teeth/${toothId}/findings`, {
      method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ description: 'Baseline' }),
    }), { params: { id: String(toothId) } })
    const finding = (await resFinding.json()).finding
    const resTreat = await POST_CREATE_TREATMENT(new NextRequest(`http://localhost:3000/api/clinical/findings/${finding.id}/treatments`, {
      method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ procedureName: 'Sealant', cost: 120 }),
    }), { params: { id: String(finding.id) } })
    const invoice = (await resTreat.json()).invoice
    const invoiceId: number = invoice.id

    // Try invalid treatment (empty fields) → expect 400 and no changes
    const invalidRes = await POST_CREATE_TREATMENT(new NextRequest(`http://localhost:3000/api/clinical/findings/${finding.id}/treatments`, {
      method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ procedureName: '', cost: '' }),
    }), { params: { id: String(finding.id) } })
    expect(invalidRes.status).toBe(400)

    const treatmentsCount = await prisma.treatment.count({ where: { findingId: finding.id } })
    expect(treatmentsCount).toBe(1)

    const inv = await getInvoiceForPatient(patientId)
    expect(inv?.id).toBe(invoiceId)
    expect(Number(inv!.totalAmount)).toBe(120)
    expect(Number(inv!.balanceDue)).toBe(120)
  })
})
