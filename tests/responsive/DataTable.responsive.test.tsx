import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import * as React from 'react'
import { DataTable, type ColumnDef } from '@/components/ui/DataTable'

interface Row { id: number; name: string; phone: string }

const rows: Row[] = [
  { id: 1, name: '<PERSON>', phone: '******-111' },
  { id: 2, name: '<PERSON>', phone: '******-222' },
]

const columns: ColumnDef<Row>[] = [
  { id: 'name', header: 'Name', accessor: (r) => r.name },
  { id: 'phone', header: 'Phone', accessor: (r) => r.phone },
]

describe('DataTable responsive layout', () => {
  it('renders mobile card layout alongside table markup when responsive=true', () => {
    render(<DataTable data={rows} columns={columns} responsive />)
    // Headers exist (table markup)
    expect(screen.getAllByText('Name').length).toBeGreaterThan(0)
    expect(screen.getAllByText('Phone').length).toBeGreaterThan(0)
    // Mobile cards include label-value pairs (labels appear again inside cards)
    // We expect at least two occurrences of label texts (table header + card labels)
    expect(screen.getAllByText('Name').length).toBeGreaterThan(1)
    expect(screen.getAllByText('Phone').length).toBeGreaterThan(1)
  })
})


