import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'
import * as React from 'react'
import { DentalChart } from '@/components/clinical/DentalChart'

describe('Dental<PERSON>hart gestures', () => {
  it('mounts with pinch-zoom handlers present', () => {
    const { container } = render(<DentalChart /> as any)
    const zoomContainer = container.querySelector('[style*="transform"]') as HTMLElement | null
    // At least ensure component renders an element which will be transformed
    expect(zoomContainer).toBeTruthy()
  })
})


