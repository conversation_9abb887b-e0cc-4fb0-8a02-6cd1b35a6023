import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import * as React from 'react'
import { SidebarProvider, Sidebar, SidebarContent, SidebarMenu, SidebarMenuItem, SidebarMenuButton, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar'

describe('Keyboard navigation', () => {
  it('focuses sidebar trigger and toggles with keyboard', async () => {
    const user = userEvent.setup()
    render(
      <SidebarProvider>
        <Sidebar />
        <SidebarInset>
          <SidebarTrigger />
          <nav>
            <SidebarContent>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuButton>One</SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarContent>
          </nav>
        </SidebarInset>
      </SidebarProvider>
    )
    const trigger = screen.getByRole('button', { name: /toggle sidebar/i })
    await user.tab()
    expect(trigger).toHaveFocus()
    await user.keyboard('{Enter}')
    // No assertion on visual state here (jsdom); just ensure no errors
    expect(trigger).toBeInTheDocument()
  })
})


