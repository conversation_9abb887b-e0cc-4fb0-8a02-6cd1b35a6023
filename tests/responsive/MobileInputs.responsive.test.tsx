import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import * as React from 'react'
import { PatientForm } from '@/components/patients/PatientForm'

describe('Mobile-friendly inputs', () => {
  it('uses tel/email/date types for appropriate fields', () => {
    render(<PatientForm onSubmit={async () => {}} onCancel={() => {}} />)
    expect(screen.getByLabelText('Phone Number')).toHaveAttribute('type', 'tel')
    expect(screen.getByLabelText('Email')).toHaveAttribute('type', 'email')
    expect(screen.getByLabelText('Date of Birth')).toHaveAttribute('type', 'date')
  })
})


