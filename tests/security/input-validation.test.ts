import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { ValidationUtils, SanitizationUtils, SecurityErrorHandler } from '@/lib/auth/utils';

describe('Input Validation Security Tests', () => {
  describe('ValidationUtils Security Checks', () => {
    it('should detect SQL injection patterns', () => {
      const sqlInjectionInputs = [
        "admin'; DROP TABLE users; --",
        "1' OR '1'='1",
        "1; UPDATE users SET password = 'hacked'",
        "UNION SELECT * FROM users",
        "admin'/**/OR/**/1=1--",
        "'; INSERT INTO users VALUES('hacker','password'); --",
      ];

      sqlInjectionInputs.forEach(input => {
        const result = ValidationUtils.hasSqlInjectionPatterns(input);
        expect(result).toBe(true);
      });
    });

    it('should not flag normal input as SQL injection', () => {
      const normalInputs = [
        'john_doe',
        '<EMAIL>',
        'password123',
        'My Dental Clinic',
        '123 Main Street',
        '******-123-4567',
      ];

      normalInputs.forEach(input => {
        const result = ValidationUtils.hasSqlInjectionPatterns(input);
        expect(result).toBe(false);
      });
    });

    it('should detect XSS patterns', () => {
      const xssInputs = [
        '<script>alert("xss")</script>',
        '<iframe src="javascript:alert(1)"></iframe>',
        'javascript:alert(document.cookie)',
        '<img onerror="alert(1)" src="x">',
        '<object data="javascript:alert(1)">',
        '<embed src="javascript:alert(1)">',
        'onload=alert(1)',
        'onclick=alert(1)',
      ];

      xssInputs.forEach(input => {
        const result = ValidationUtils.hasXssPatterns(input);
        expect(result).toBe(true);
      });
    });

    it('should not flag normal input as XSS', () => {
      const normalInputs = [
        'john_doe',
        '<EMAIL>',
        'password123!',
        'My Dental Clinic',
        '123 Main Street, Apt 4B',
        'Phone: ******-123-4567',
      ];

      normalInputs.forEach(input => {
        const result = ValidationUtils.hasXssPatterns(input);
        expect(result).toBe(false);
      });
    });

    it('should validate security threats comprehensively', () => {
      const maliciousInputs = [
        {
          input: "admin'; DROP TABLE users; --",
          expectedThreats: ['Potential SQL injection detected'],
        },
        {
          input: '<script>alert("xss")</script>',
          expectedThreats: ['Potential XSS attack detected'],
        },
        {
          input: 'a'.repeat(15000),
          expectedThreats: ['Input too long - potential DoS attack'],
        },
        {
          input: "admin'<script>alert(1)</script>; DROP TABLE users; --",
          expectedThreats: ['Potential SQL injection detected', 'Potential XSS attack detected'],
        },
      ];

      maliciousInputs.forEach(({ input, expectedThreats }) => {
        const result = ValidationUtils.validateSecurityThreats(input);
        expect(result.isValid).toBe(false);
        expectedThreats.forEach(threat => {
          expect(result.threats).toContain(threat);
        });
      });
    });

    it('should pass clean input through security validation', () => {
      const cleanInputs = [
        'john_doe',
        '<EMAIL>',
        'SecurePassword123!',
        'Downtown Dental Clinic',
        '123 Main Street, Suite 200',
        '******-123-4567',
      ];

      cleanInputs.forEach(input => {
        const result = ValidationUtils.validateSecurityThreats(input);
        expect(result.isValid).toBe(true);
        expect(result.threats).toHaveLength(0);
      });
    });
  });

  describe('SanitizationUtils Security Tests', () => {
    it('should sanitize dangerous characters from strings', () => {
      const testCases = [
        {
          input: "user<script>alert('xss')</script>name",
          expected: "userscriptalert(xss)scriptname",
        },
        {
          input: "clinic'name\"with'quotes",
          expected: "clinicnamewithquotes",
        },
        {
          input: "path/traversal\\attack",
          expected: "pathtraversalattack",
        },
        {
          input: "user&amp;name",
          expected: "user&amp;amp;name",
        },
      ];

      testCases.forEach(({ input, expected }) => {
        const result = SanitizationUtils.sanitizeString(input);
        expect(result).toBe(expected);
      });
    });

    it('should enforce length limits during sanitization', () => {
      const longString = 'a'.repeat(300);
      const sanitizedString = SanitizationUtils.sanitizeString(longString);
      expect(sanitizedString.length).toBe(255);
    });

    it('should sanitize email addresses consistently', () => {
      const testCases = [
        {
          input: '<EMAIL>',
          expected: '<EMAIL>',
        },
        {
          input: '  <EMAIL>  ',
          expected: '<EMAIL>',
        },
        {
          input: 'a'.repeat(300) + '@example.com',
          expected: ('a'.repeat(300) + '@example.com').substring(0, 255),
        },
      ];

      testCases.forEach(({ input, expected }) => {
        const result = SanitizationUtils.sanitizeEmail(input);
        expect(result).toBe(expected);
      });
    });

    it('should sanitize phone numbers properly', () => {
      const testCases = [
        {
          input: '******-123-4567',
          expected: '******-123-4567',
        },
        {
          input: '555.123.4567abc!@#',
          expected: '5551234567',
        },
        {
          input: '(*************',
          expected: '(*************',
        },
        {
          input: '+1' + '1'.repeat(30),
          expected: ('+1' + '1'.repeat(30)).substring(0, 20),
        },
      ];

      testCases.forEach(({ input, expected }) => {
        const result = SanitizationUtils.sanitizePhone(input);
        expect(result).toBe(expected);
      });
    });

    it('should sanitize URLs securely', () => {
      const testCases = [
        {
          input: 'https://example.com/path',
          expected: 'https://example.com/path',
        },
        {
          input: 'http://example.com/path',
          expected: 'http://example.com/path',
        },
        {
          input: 'ftp://malicious.com/',
          expected: '',
        },
        {
          input: 'javascript:alert(1)',
          expected: '',
        },
        {
          input: 'data:text/html,<script>alert(1)</script>',
          expected: '',
        },
      ];

      testCases.forEach(({ input, expected }) => {
        const result = SanitizationUtils.sanitizeUrl(input);
        expect(result).toBe(expected);
      });
    });

    it('should remove dangerous characters comprehensively', () => {
      const dangerousInput = `user<script>name{"key":"value"}[array];command|pipe&background$variable\`backtick\``;
      const result = SanitizationUtils.removeDangerousChars(dangerousInput);
      
      // Should not contain any dangerous characters
      expect(result).not.toMatch(/[<>'"&{}[\];|&$`]/);
      expect(result).toBe('userscriptnamekey:valuearraycommandpipebackgroundvariablebacktick');
    });

    it('should sanitize for database operations', () => {
      const maliciousInput = "user'name\x00\x08\x09\x1a\nwith\r\"special\\chars%";
      const result = SanitizationUtils.sanitizeForDatabase(maliciousInput);
      
      // Should not contain any SQL injection characters
      expect(result).not.toMatch(/[\0\x08\x09\x1a\n\r"'\\%]/);
      expect(result).toBe('usernamewithspecialchars');
    });
  });

  describe('SecurityErrorHandler Tests', () => {
    it('should create standardized validation error responses', () => {
      const errors = ['Email is required', 'Password too weak'];
      const response = SecurityErrorHandler.createValidationErrorResponse(
        'Validation failed',
        errors,
        422
      );

      expect(response).toMatchObject({
        success: false,
        message: 'Validation failed',
        errors,
        statusCode: 422,
      });
      expect(response.timestamp).toBeDefined();
      expect(new Date(response.timestamp)).toBeInstanceOf(Date);
    });

    it('should create standardized security error responses', () => {
      const response = SecurityErrorHandler.createSecurityErrorResponse(
        'Malicious input detected',
        403
      );

      expect(response).toMatchObject({
        success: false,
        message: 'Malicious input detected',
        statusCode: 403,
      });
      expect(response.timestamp).toBeDefined();
      expect(new Date(response.timestamp)).toBeInstanceOf(Date);
    });

    it('should create standardized rate limit error responses', () => {
      const retryAfter = 1800;
      const response = SecurityErrorHandler.createRateLimitErrorResponse(retryAfter);

      expect(response).toMatchObject({
        success: false,
        message: 'Too many requests. Please try again later.',
        statusCode: 429,
        retryAfter,
      });
      expect(response.timestamp).toBeDefined();
      expect(new Date(response.timestamp)).toBeInstanceOf(Date);
    });
  });

  describe('Edge Cases and Attack Vectors', () => {
    it('should handle null and undefined inputs safely', () => {
      // These should not throw errors
      expect(() => {
        SanitizationUtils.sanitizeString('');
        SanitizationUtils.sanitizeEmail('');
        SanitizationUtils.sanitizePhone('');
      }).not.toThrow();
    });

    it('should handle very long inputs without performance issues', () => {
      const veryLongInput = 'a'.repeat(100000);
      const startTime = Date.now();
      
      const result = ValidationUtils.validateSecurityThreats(veryLongInput);
      
      const endTime = Date.now();
      const executionTime = endTime - startTime;
      
      // Should complete within reasonable time (1 second)
      expect(executionTime).toBeLessThan(1000);
      expect(result.isValid).toBe(false);
      expect(result.threats).toContain('Input too long - potential DoS attack');
    });

    it('should detect nested and encoded injection attempts', () => {
      const nestedInjections = [
        "admin'/**/UNION/**/SELECT/**/password/**/FROM/**/users--",
        "1' AND (SELECT SUBSTRING(password,1,1) FROM users WHERE username='admin')='a",
        "';waitfor delay '00:00:05'--",
        "%3Cscript%3Ealert(1)%3C/script%3E", // URL encoded
      ];

      nestedInjections.forEach(input => {
        const result = ValidationUtils.validateSecurityThreats(input);
        expect(result.isValid).toBe(false);
        expect(result.threats.length).toBeGreaterThan(0);
      });
    });

    it('should handle unicode and international characters safely', () => {
      const internationalInputs = [
        'José García',
        '北京市',
        'Café Münzen',
        'Пользователь',
        'العربية',
      ];

      internationalInputs.forEach(input => {
        const sanitized = SanitizationUtils.sanitizeString(input);
        const securityCheck = ValidationUtils.validateSecurityThreats(input);
        
        // Should not be flagged as malicious
        expect(securityCheck.isValid).toBe(true);
        // Should preserve international characters (minus dangerous ones)
        expect(sanitized.length).toBeGreaterThan(0);
      });
    });
  });
});

describe('Integration Security Tests', () => {
  it('should validate complete registration payload security', () => {
    const maliciousRegistrationPayload = {
      clinic: {
        name: "Evil Clinic<script>alert('xss')</script>",
        address: "123 Main St'; DROP TABLE tenants; --",
        phone: "******-123-4567<script>",
        email: "<EMAIL>'><script>alert(1)</script>",
      },
      adminUser: {
        username: "admin'; UPDATE users SET password='hacked' WHERE id=1; --",
        email: "<EMAIL><script>alert(document.cookie)</script>",
        firstName: "John<iframe src='javascript:alert(1)'></iframe>",
        lastName: "Doe'; DROP TABLE users; --",
        password: "password123",
        confirmPassword: "password123",
      },
    };

    const payloadString = JSON.stringify(maliciousRegistrationPayload);
    const securityCheck = ValidationUtils.validateSecurityThreats(payloadString);
    
    expect(securityCheck.isValid).toBe(false);
    expect(securityCheck.threats).toContain('Potential SQL injection detected');
    expect(securityCheck.threats).toContain('Potential XSS attack detected');
  });

  it('should validate complete login payload security', () => {
    const maliciousLoginPayload = {
      username: "admin'/**/OR/**/1=1/**/--",
      password: "<script>fetch('/api/admin/users').then(r=>r.json()).then(d=>console.log(d))</script>",
    };

    const payloadString = JSON.stringify(maliciousLoginPayload);
    const securityCheck = ValidationUtils.validateSecurityThreats(payloadString);
    
    expect(securityCheck.isValid).toBe(false);
    expect(securityCheck.threats).toContain('Potential SQL injection detected');
    expect(securityCheck.threats).toContain('Potential XSS attack detected');
  });

  it('should pass clean payloads through security validation', () => {
    const cleanRegistrationPayload = {
      clinic: {
        name: "Downtown Dental Clinic",
        address: "123 Main Street, Suite 200",
        phone: "******-123-4567",
        email: "<EMAIL>",
      },
      adminUser: {
        username: "admin_user",
        email: "<EMAIL>",
        firstName: "John",
        lastName: "Doe",
        password: "SecurePassword123!",
        confirmPassword: "SecurePassword123!",
      },
    };

    const payloadString = JSON.stringify(cleanRegistrationPayload);
    const securityCheck = ValidationUtils.validateSecurityThreats(payloadString);
    
    expect(securityCheck.isValid).toBe(true);
    expect(securityCheck.threats).toHaveLength(0);
  });
});
