import { describe, it, expect } from 'vitest'
import { generateFDINumbers, calculateQuadrant, calculatePosition, isValidFDINumber, getToothName, getToothType, type ToothType } from '@/lib/clinical/fdi-numbering'

describe('FDI Numbering Utilities', () => {
  it('generates all 32 permanent dentition FDI numbers in order', () => {
    const nums = generateFDINumbers()
    expect(nums.length).toBe(32)
    expect(nums.at(0)).toBe(11)
    expect(nums.at(-1)).toBe(48)

    // Ensure each quadrant has positions 1..8
    const byQuadrant = new Map<number, number[]>()
    nums.forEach(n => {
      const q = Math.floor(n / 10)
      const arr = byQuadrant.get(q) ?? []
      arr.push(n % 10)
      byQuadrant.set(q, arr)
    })
    for (const q of [1, 2, 3, 4]) {
      const positions = (byQuadrant.get(q) ?? []).sort((a, b) => a - b)
      expect(positions).toEqual([1,2,3,4,5,6,7,8])
    }
  })

  it('validates FDI numbers correctly', () => {
    // Valid edges
    expect(isValidFDINumber(11)).toBe(true)
    expect(isValidFDINumber(18)).toBe(true)
    expect(isValidFDINumber(21)).toBe(true)
    expect(isValidFDINumber(28)).toBe(true)
    expect(isValidFDINumber(31)).toBe(true)
    expect(isValidFDINumber(38)).toBe(true)
    expect(isValidFDINumber(41)).toBe(true)
    expect(isValidFDINumber(48)).toBe(true)

    // Invalid
    expect(isValidFDINumber(10)).toBe(false)
    expect(isValidFDINumber(19)).toBe(false)
    expect(isValidFDINumber(49)).toBe(false)
    expect(isValidFDINumber(0)).toBe(false)
    expect(isValidFDINumber(99)).toBe(false)
  })

  it('calculates quadrant and position for valid inputs', () => {
    expect(calculateQuadrant(11)).toBe(1)
    expect(calculatePosition(11)).toBe(1)

    expect(calculateQuadrant(28)).toBe(2)
    expect(calculatePosition(28)).toBe(8)

    expect(calculateQuadrant(31)).toBe(3)
    expect(calculatePosition(31)).toBe(1)

    expect(calculateQuadrant(48)).toBe(4)
    expect(calculatePosition(48)).toBe(8)
  })

  it('throws for invalid quadrant/position calculations', () => {
    const invalids = [10, 19, 29, 30, 39, 40, 49, 99, -1, 0, 5.5]
    invalids.forEach((n) => {
      expect(() => calculateQuadrant(n as number)).toThrowError()
      expect(() => calculatePosition(n as number)).toThrowError()
    })
  })

  it('provides correct tooth names mapping', () => {
    expect(getToothName(11)).toBe('Upper Right Central Incisor')
    expect(getToothName(12)).toBe('Upper Right Lateral Incisor')
    expect(getToothName(13)).toBe('Upper Right Canine')
    expect(getToothName(14)).toBe('Upper Right First Premolar')
    expect(getToothName(15)).toBe('Upper Right Second Premolar')
    expect(getToothName(16)).toBe('Upper Right First Molar')
    expect(getToothName(17)).toBe('Upper Right Second Molar')
    expect(getToothName(18)).toBe('Upper Right Third Molar')

    expect(getToothName(21)).toBe('Upper Left Central Incisor')
    expect(getToothName(28)).toBe('Upper Left Third Molar')

    expect(getToothName(31)).toBe('Lower Left Central Incisor')
    expect(getToothName(38)).toBe('Lower Left Third Molar')

    expect(getToothName(41)).toBe('Lower Right Central Incisor')
    expect(getToothName(48)).toBe('Lower Right Third Molar')
  })

  it('provides correct tooth types mapping', () => {
    const incisor: ToothType = 'Incisor'
    const canine: ToothType = 'Canine'
    const premolar: ToothType = 'Premolar'
    const molar: ToothType = 'Molar'

    expect(getToothType(11)).toBe(incisor)
    expect(getToothType(12)).toBe(incisor)
    expect(getToothType(13)).toBe(canine)
    expect(getToothType(14)).toBe(premolar)
    expect(getToothType(15)).toBe(premolar)
    expect(getToothType(16)).toBe(molar)
    expect(getToothType(17)).toBe(molar)
    expect(getToothType(18)).toBe(molar)
  })

  it('throws for invalid inputs when requesting names/types', () => {
    const invalids = [10, 19, 0, 99, -5, 2.2]
    invalids.forEach((n) => {
      expect(() => getToothName(n as number)).toThrowError()
      expect(() => getToothType(n as number)).toThrowError()
    })
  })

  it('provides correct tooth names for all valid FDI numbers', () => {
    const quadrantName: Record<number, string> = {
      1: 'Upper Right',
      2: 'Upper Left',
      3: 'Lower Left',
      4: 'Lower Right',
    }
    const positionName: Record<number, string> = {
      1: 'Central Incisor',
      2: 'Lateral Incisor',
      3: 'Canine',
      4: 'First Premolar',
      5: 'Second Premolar',
      6: 'First Molar',
      7: 'Second Molar',
      8: 'Third Molar',
    }

    for (const n of generateFDINumbers()) {
      const q = Math.floor(n / 10)
      const p = n % 10
      const expected = `${quadrantName[q]} ${positionName[p]}`
      expect(getToothName(n)).toBe(expected)
    }
  })

  it('provides correct tooth types for all valid FDI numbers', () => {
    const typeForPosition: Record<number, ToothType> = {
      1: 'Incisor',
      2: 'Incisor',
      3: 'Canine',
      4: 'Premolar',
      5: 'Premolar',
      6: 'Molar',
      7: 'Molar',
      8: 'Molar',
    }

    for (const n of generateFDINumbers()) {
      const pos = n % 10
      expect(getToothType(n)).toBe(typeForPosition[pos])
    }
  })
})
