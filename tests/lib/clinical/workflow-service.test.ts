import { describe, it, expect } from 'vitest'
import prisma from '@/lib/prisma'
import { ClinicalWorkflowService, WorkflowError } from '@/lib/clinical/workflow-service'

async function createTenant(nameSuffix: string = '') {
  return prisma.tenant.create({ data: { name: `Test Tenant${nameSuffix}` } })
}

async function getAnyToothIdForPatient(patientId: number) {
  const caseSheet = await prisma.caseSheet.findFirst({ where: { patientId }, include: { teeth: true } })
  if (!caseSheet || caseSheet.teeth.length === 0) throw new Error('No teeth found for patient')
  return caseSheet.teeth[0].id
}

describe('ClinicalWorkflowService', () => {
  it('createPatientWithCompleteWorkflow creates patient, user, case sheet and 32 teeth', async () => {
    const tenant = await createTenant('-workflow-success')
    const svc = new ClinicalWorkflowService()

    const result = await svc.createPatientWithCompleteWorkflow({
      firstName: 'John',
      lastName: 'Doe',
      phoneNumber: '**********',
      tenantId: tenant.id,
      email: '<EMAIL>',
    })

    expect(result.patient?.id).toBeTruthy()
    expect(result.caseSheet?.id).toBeTruthy()
    expect(result.teethCount).toBe(32)

    // Validate user linkage and type
    const patient = await prisma.patient.findUnique({ where: { id: result.patient!.id } })
    expect(patient?.userId).toBeTruthy()
    const user = await prisma.user.findUnique({ where: { id: patient!.userId! } })
    expect(user?.phoneNumber).toBe('**********')
    expect(user?.userType).toBe('PATIENT')

    // Validate teeth truly exist
    const teethCount = await prisma.tooth.count({ where: { caseSheetId: result.caseSheet!.id } })
    expect(teethCount).toBe(32)
  })

  it('createPatientWithCompleteWorkflow validates inputs and throws WorkflowError', async () => {
    const svc = new ClinicalWorkflowService()
    await expect(
      svc.createPatientWithCompleteWorkflow({ firstName: '', lastName: '', phoneNumber: '', tenantId: '' })
    ).rejects.toBeInstanceOf(WorkflowError)
  })

  it('createPatientWithCompleteWorkflow prevents duplicate patients per tenant', async () => {
    const tenant = await createTenant('-duplicate')
    // Seed an existing patient with same first/last in tenant
    await prisma.patient.create({
      data: {
        tenantId: tenant.id,
        firstName: 'Jane',
        lastName: 'Smith',
      },
    })

    const svc = new ClinicalWorkflowService()
    await expect(
      svc.createPatientWithCompleteWorkflow({
        firstName: 'Jane',
        lastName: 'Smith',
        phoneNumber: '**********',
        tenantId: tenant.id,
      })
    ).rejects.toBeInstanceOf(WorkflowError)
  })

  it('createPatientWithCompleteWorkflow rolls back on user unique violation', async () => {
    const tenant = await createTenant('-rollback')
    // Pre-create conflicting user with same phone in this tenant
    await prisma.user.create({
      data: {
        tenantId: tenant.id,
        username: '12345',
        phoneNumber: '12345',
        password: 'x',
        userType: 'PATIENT',
      },
    })

    const svc = new ClinicalWorkflowService()
    await expect(
      svc.createPatientWithCompleteWorkflow({
        firstName: 'Jake',
        lastName: 'Rollback',
        phoneNumber: '12345',
        tenantId: tenant.id,
      })
    ).rejects.toBeInstanceOf(WorkflowError)

    // Verify patient was not persisted due to transaction rollback
    const exists = await prisma.patient.findFirst({
      where: { tenantId: tenant.id, firstName: 'Jake', lastName: 'Rollback' },
    })
    expect(exists).toBeNull()
  })

  it('createCaseSheetWithTeeth creates chart with 32 teeth and prevents duplicates', async () => {
    const tenant = await createTenant('-casesheet')
    // Create patient without case sheet
    const patient = await prisma.patient.create({
      data: {
        tenantId: tenant.id,
        firstName: 'Case',
        lastName: 'Subject',
      },
    })

    const svc = new ClinicalWorkflowService()
    const { caseSheet, teethCount } = await svc.createCaseSheetWithTeeth(patient.id)
    expect(caseSheet.id).toBeTruthy()
    expect(teethCount).toBe(32)

    // Second attempt should throw
    await expect(svc.createCaseSheetWithTeeth(patient.id)).rejects.toBeInstanceOf(WorkflowError)
  })

  it('createFindingWithTreatment creates finding alone and with inline treatment', async () => {
    const tenant = await createTenant('-finding')
    // Seed patient with case sheet + teeth using service
    const svc = new ClinicalWorkflowService()
    const { patient, caseSheet } = await svc.createPatientWithCompleteWorkflow({
      firstName: 'Find',
      lastName: 'Alone',
      phoneNumber: '**********',
      tenantId: tenant.id,
    })
    expect(patient?.id).toBeTruthy()
    expect(caseSheet?.id).toBeTruthy()

    const toothId = await getAnyToothIdForPatient(patient!.id)

    // Finding only
    const res1 = await svc.createFindingWithTreatment({ toothId, description: 'Caries on occlusal' })
    expect(res1.finding.id).toBeTruthy()
    expect(res1.treatment).toBeUndefined()

    // With inline treatment
    const res2 = await svc.createFindingWithTreatment({
      toothId,
      description: 'Fracture',
      procedureName: 'Composite restoration',
      cost: 200,
    })
    expect(res2.finding.id).toBeTruthy()
    expect(res2.treatment?.id).toBeTruthy()
  })

  it('createTreatmentWithInvoice creates treatment and creates/updates invoice, linking treatment to invoice', async () => {
    const tenant = await createTenant('-treatment')
    const svc = new ClinicalWorkflowService()

    const { patient } = await svc.createPatientWithCompleteWorkflow({
      firstName: 'Treat',
      lastName: 'Me',
      phoneNumber: '**********',
      tenantId: tenant.id,
    })
    const toothId = await getAnyToothIdForPatient(patient!.id)

    // Create a finding
    const { finding } = await svc.createFindingWithTreatment({ toothId, description: 'Deep caries' })

    // Add first treatment
    const { treatment, invoice } = await svc.createTreatmentWithInvoice({
      findingId: finding.id,
      procedureName: 'Root canal therapy',
      cost: 800,
    })
    expect(treatment.id).toBeTruthy()
    expect((treatment as any).invoiceId).toBe(invoice.id)
    expect(invoice.id).toBeTruthy()
    const inv1 = await prisma.invoice.findUnique({ where: { id: invoice.id } })
    expect(Number(inv1!.totalAmount)).toBe(800)
    expect(Number(inv1!.balanceDue)).toBe(800)

    // Add second treatment updates same invoice totals
    const { invoice: invoice2 } = await svc.createTreatmentWithInvoice({
      findingId: finding.id,
      procedureName: 'Crown',
      cost: 1200,
    })
    const inv2 = await prisma.invoice.findUnique({ where: { id: invoice2.id } })
    expect(Number(inv2!.totalAmount)).toBe(2000)
    expect(Number(inv2!.balanceDue)).toBe(2000)
  })

  it('processPaymentWithUpdate records payments and updates invoice status/balances, including overpayment clamp', async () => {
    const tenant = await createTenant('-payment')
    const svc = new ClinicalWorkflowService()

    const { patient } = await svc.createPatientWithCompleteWorkflow({
      firstName: 'Pay',
      lastName: 'Later',
      phoneNumber: '**********',
      tenantId: tenant.id,
    })
    const toothId = await getAnyToothIdForPatient(patient!.id)

    const { finding } = await svc.createFindingWithTreatment({ toothId, description: 'Caries' })
    const { invoice } = await svc.createTreatmentWithInvoice({ findingId: finding.id, procedureName: 'Filling', cost: 200 })

    // Partial payment should set status to SENT and reduce balance
    const { payment: payment1 } = await svc.processPaymentWithUpdate({ patientId: patient!.id, invoiceId: invoice.id, amount: 50, paymentMethod: 'CASH' })
    expect(payment1.id).toBeTruthy()
    const invAfter1 = await prisma.invoice.findUnique({ where: { id: invoice.id } })
    expect(invAfter1?.status === 'SENT' || invAfter1?.status === 'PAID').toBeTruthy()
    expect(Number(invAfter1!.amountPaid)).toBe(50)
    expect(Number(invAfter1!.balanceDue)).toBe(150)

    // Overpayment: pay more than remaining -> clamp to zero and PAID
    const { invoice: invAfter2 } = await svc.processPaymentWithUpdate({ patientId: patient!.id, invoiceId: invoice.id, amount: 200, paymentMethod: 'CASH' })
    expect(invAfter2).toBeTruthy()
    expect(Number(invAfter2!.balanceDue)).toBe(0)
    expect(invAfter2!.status).toBe('PAID')
  })

  it('processPaymentWithUpdate validates input and handles missing invoice gracefully', async () => {
    const svc = new ClinicalWorkflowService()
    // Invalid patientId
    await expect(
      // @ts-expect-error intentional invalid
      svc.processPaymentWithUpdate({ patientId: 0, amount: 10, paymentMethod: 'CASH' })
    ).rejects.toBeInstanceOf(WorkflowError)
  })
})
