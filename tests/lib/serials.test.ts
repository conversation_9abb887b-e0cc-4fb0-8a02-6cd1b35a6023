import { describe, it, expect, beforeEach } from 'vitest'
import { PrismaClient } from '@prisma/client'
import { generateCaseSheetSerial, generateInvoiceSerial, generatePaymentSerial } from '@/lib/serials'

const prisma = new PrismaClient()

async function createTenant() {
  const id = `tenant-${Date.now()}-${Math.random()}`
  return prisma.tenant.create({ data: { id, name: `Tenant ${id}` } })
}

describe('Serial Generators', () => {
  beforeEach(async () => {
    // Clean DB between tests if needed; using separate tenant ids ensures isolation
  })

  it('generates case sheet serials incrementally and records entries', async () => {
    const t = await createTenant()
    const s1 = await generateCaseSheetSerial(t.id)
    const s2 = await generateCaseSheetSerial(t.id)
    expect(s1).toBe('1')
    expect(s2).toBe('2')
    const count = await prisma.caseSheetSerial.count({ where: { tenantId: t.id } })
    expect(count).toBe(2)
  })

  it('generates invoice serials incrementally and records entries', async () => {
    const t = await createTenant()
    const s1 = await generateInvoiceSerial(t.id)
    const s2 = await generateInvoiceSerial(t.id)
    expect(s1).toBe('1')
    expect(s2).toBe('2')
    const count = await prisma.invoiceSerial.count({ where: { tenantId: t.id } })
    expect(count).toBe(2)
  })

  it('generates payment serials incrementally and records entries', async () => {
    const t = await createTenant()
    const s1 = await generatePaymentSerial(t.id)
    const s2 = await generatePaymentSerial(t.id)
    expect(s1).toBe('1')
    expect(s2).toBe('2')
    const count = await prisma.paymentSerial.count({ where: { tenantId: t.id } })
    expect(count).toBe(2)
  })

  it('isolates serials per tenant', async () => {
    const t1 = await createTenant()
    const t2 = await createTenant()
    const a1 = await generateInvoiceSerial(t1.id)
    const b1 = await generateInvoiceSerial(t2.id)
    const a2 = await generateInvoiceSerial(t1.id)
    const b2 = await generateInvoiceSerial(t2.id)
    expect([a1, a2]).toEqual(['1', '2'])
    expect([b1, b2]).toEqual(['1', '2'])
  })

  it('supports concurrent generation without duplicates (best-effort)', async () => {
    const t = await createTenant()
    const results = await Promise.all(Array.from({ length: 10 }).map(() => generateInvoiceSerial(t.id)))
    const set = new Set(results)
    expect(set.size).toBe(10)
  })
})


