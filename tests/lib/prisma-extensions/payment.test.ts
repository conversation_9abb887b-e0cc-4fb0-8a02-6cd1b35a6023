import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { PrismaClient, PaymentMethod, InvoiceStatus } from '@prisma/client'
import { applyAllExtensions } from '@/lib/prisma-extensions'
import { getPrismaWithExtensions } from './_setup-prisma-extensions'

const prisma = getPrismaWithExtensions()

async function createTenant() {
  const id = `tenant-${Date.now()}-${Math.random()}`
  return prisma.tenant.create({ data: { id, name: `Tenant ${id}` } })
}

let tenantId: string
let patientId: number
let invoiceId: number

async function seedOnce() {
  const t = await createTenant()
  tenantId = t.id
  const suffix = Math.random().toString(36).slice(2, 7)
  const patient = await prisma.patient.create({ data: { tenantId, firstName: `Pay-${suffix}`, lastName: `Ment-${suffix}` } })
  patientId = patient.id
  const invoice = await (prisma as any).invoice.createInvoice({ tenantId, patientId: patient.id, invoiceDate: new Date(), totalAmount: 500 as any })
  invoiceId = invoice.id
}

describe.skip('Payment Extension', () => {
  beforeAll(async () => {
    await seedOnce()
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  it('processes payment, updates invoice aggregates, and lists payments', async () => {
    const p1 = await (prisma as any).payment.processPayment(patientId, invoiceId, 200, PaymentMethod.CARD)
    expect(p1.id).toBeTruthy()

    const p2 = await (prisma as any).payment.processPayment(patientId, invoiceId, '150', PaymentMethod.CASH)
    expect(p2.id).toBeTruthy()

    const payments = await prisma.payment.findByInvoice(invoiceId)
    expect(payments.length).toBeGreaterThanOrEqual(2)

    const invoice = await prisma.invoice.findUnique({ where: { id: invoiceId } })
    expect(Number(invoice?.amountPaid)).toBeGreaterThanOrEqual(350)
    expect(Number(invoice?.balanceDue)).toBeLessThanOrEqual(150)
    expect([InvoiceStatus.SENT, InvoiceStatus.PAID]).toContain(invoice?.status)

    const balance = await prisma.payment.calculateInvoiceBalance(invoiceId)
    expect(balance.amountPaid).toBeGreaterThanOrEqual(350)
    expect(balance.balanceDue).toBeLessThanOrEqual(150)
  })

  it('allows standalone patient payment (no invoice)', async () => {
    const p = await (prisma as any).payment.processPayment(patientId, null, 50, PaymentMethod.OTHER)
    expect(p.id).toBeTruthy()
    expect(p.invoiceId).toBeNull()
  })
})
