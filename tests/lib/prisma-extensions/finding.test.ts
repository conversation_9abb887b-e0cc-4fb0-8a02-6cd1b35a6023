import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { Prisma, PrismaClient } from '@prisma/client'
import { applyAllExtensions } from '@/lib/prisma-extensions'
import { getPrismaWithExtensions } from './_setup-prisma-extensions'
const prisma = getPrismaWithExtensions()

async function createTenant() {
  const id = `tenant-${Date.now()}-${Math.random()}`
  return prisma.tenant.create({ data: { id, name: `Tenant ${id}` } })
}

async function seedTooth(tenantId: string) {
  const suffix = Math.random().toString(36).slice(2, 7)
  const patient = await prisma.patient.create({ data: { tenantId, firstName: `Pat-${suffix}`, lastName: `Ient-${suffix}` } })
  const caseSheet = await prisma.caseSheet.create({ data: { tenantId, patientId: patient.id } })
  const tooth = await prisma.tooth.create({ data: { tenantId, caseSheetId: caseSheet.id, toothNumber: 11, quadrant: 1, positionInQuadrant: 1, toothName: 'Upper Right Central Incisor' } })
  return { tooth, patient }
}

describe('Finding Extension', () => {
  let tenantId: string
  let toothId: number

  beforeAll(async () => {
    const t = await createTenant()
    tenantId = t.id
    const { tooth } = await seedTooth(tenantId)
    toothId = tooth.id
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  it('updates finding description', async () => {
    // Create a finding directly
    const f = await prisma.finding.create({ data: { tenantId, toothId, description: 'Initial desc' } })
    const updated = await prisma.finding.updateDescription(f.id, 'Updated description', 2)
    expect(updated.description).toBe('Updated description')
  })

  it('finds finding with treatments included', async () => {
    const f = await prisma.finding.create({ data: { tenantId, toothId, description: 'Has treatments' } })
    // attach a treatment directly
    await prisma.treatment.create({ data: { tenantId, findingId: f.id, procedureName: 'Composite filling', cost: 200 } as any })
    const withTreatments = await prisma.finding.findWithTreatments(f.id)
    expect(withTreatments?.treatments?.length).toBe(1)
  })
})
