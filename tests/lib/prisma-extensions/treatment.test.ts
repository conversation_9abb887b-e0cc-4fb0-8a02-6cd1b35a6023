import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { PrismaClient, TreatmentStatus } from '@prisma/client'
import { applyAllExtensions } from '@/lib/prisma-extensions'
import { getPrismaWithExtensions } from './_setup-prisma-extensions'

const prisma = getPrismaWithExtensions()

async function createTenant() {
  const id = `tenant-${Date.now()}-${Math.random()}`
  return prisma.tenant.create({ data: { id, name: `Tenant ${id}` } })
}

async function seedFinding(tenantId: string) {
  const suffix = Math.random().toString(36).slice(2, 7)
  const patient = await prisma.patient.create({ data: { tenantId, firstName: `Al-${suffix}`, lastName: `Ma-${suffix}` } })
  const cs = await prisma.caseSheet.create({ data: { tenantId, patientId: patient.id } })
  const tooth = await prisma.tooth.create({ data: { tenantId, caseSheetId: cs.id, toothNumber: 11, quadrant: 1, positionInQuadrant: 1, toothName: 'Upper Right Central Incisor' } })
  const finding = await prisma.finding.create({ data: { tenantId, toothId: tooth.id, description: 'Caries' } })
  return { finding }
}

describe('Treatment Extension', () => {
  let tenantId: string
  let findingId: number

  beforeAll(async () => {
    const t = await createTenant()
    tenantId = t.id
    const seeded = await seedFinding(tenantId)
    findingId = seeded.finding.id
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  it('updates status and completion on existing treatment', async () => {
    const t1 = await prisma.treatment.create({ data: { tenantId, findingId, procedureName: 'Composite filling', cost: 200 } as any })
    const updated = await prisma.treatment.updateStatus(t1.id, TreatmentStatus.COMPLETED as any)
    expect(updated.status).toBe(TreatmentStatus.COMPLETED)

    const t2 = await prisma.treatment.create({ data: { tenantId, findingId, procedureName: 'Prophylaxis - adult', cost: 100 } as any })
    const completed = await prisma.treatment.markCompleted(t2.id)
    expect(completed.status).toBe(TreatmentStatus.COMPLETED)
    expect(completed.completedDate).toBeTruthy()
  })

  it('updates cost and exposes list getters', async () => {
    const t = await prisma.treatment.create({ data: { tenantId, findingId, procedureName: 'Root canal therapy', cost: 800 } as any })
    const updatedCost = await prisma.treatment.updateCost(t.id, 900)
    expect(Number(updatedCost.cost)).toBe(900)

    const list = await prisma.treatment.getTreatmentsForFinding(tenantId, findingId)
    expect(Array.isArray(list)).toBe(true)
  })
})
