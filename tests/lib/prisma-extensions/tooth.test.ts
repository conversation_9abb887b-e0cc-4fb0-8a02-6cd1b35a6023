import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { PrismaClient, ToothStatus } from '@prisma/client'
import { applyAllExtensions } from '@/lib/prisma-extensions'

import { getPrismaWithExtensions } from './_setup-prisma-extensions'
const prisma = getPrismaWithExtensions()

async function createTenant() {
  const id = `tenant-${Date.now()}-${Math.random()}`
  return prisma.tenant.create({ data: { id, name: `Tenant ${id}` } })
}

async function createPatientWithCaseSheet(tenantId: string) {
  const suffix = Math.random().toString(36).slice(2, 7)
  const patient = await prisma.patient.create({
    data: { tenantId, firstName: `Jane-${suffix}`, lastName: `Smith-${suffix}` },
  })
  const caseSheet = await prisma.caseSheet.create({
    data: { tenantId, patientId: patient.id },
  })
  return { patient, caseSheet }
}

describe('Tooth Extension', () => {
  let tenantId: string
  let caseSheetId: number

  beforeAll(async () => {
    const tenant = await createTenant()
    tenantId = tenant.id
    const { caseSheet } = await createPatientWithCaseSheet(tenantId)
    caseSheetId = caseSheet.id
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  it('creates a tooth with auto-populated FDI fields and prevents duplicates', async () => {
    const tooth = await prisma.tooth.createTooth({
      tenantId,
      caseSheetId,
      toothNumber: 11,
    })

    expect(tooth.id).toBeTruthy()
    expect(tooth.quadrant).toBe(1)
    expect(tooth.positionInQuadrant).toBe(1)
    expect(tooth.toothName).toContain('Upper Right')
    expect(tooth.status).toBe(ToothStatus.PRESENT)

    await expect(
      prisma.tooth.createTooth({ tenantId, caseSheetId, toothNumber: 11 })
    ).rejects.toThrowError()
  })

  it('creates all 32 teeth for a case sheet and queries by quadrant and status', async () => {
    const { caseSheet } = await createPatientWithCaseSheet(tenantId)

    const result = await prisma.tooth.createCompleteSet(caseSheet.id, tenantId)
    expect(result.count).toBe(32)

    const q1 = await prisma.tooth.getTeethByQuadrant(caseSheet.id, 1, tenantId)
    expect(q1.length).toBe(8)

    const present = await prisma.tooth.getTeethByStatus(caseSheet.id, ToothStatus.PRESENT, tenantId)
    expect(present.length).toBe(32)
  })
})
