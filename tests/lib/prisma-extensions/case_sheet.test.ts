import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { PrismaClient, CaseSheetStatus } from '@prisma/client'
import { applyAllExtensions } from '@/lib/prisma-extensions'

import { getPrismaWithExtensions } from './_setup-prisma-extensions'
const prisma = getPrismaWithExtensions()

async function createTenant() {
  const id = `tenant-${Date.now()}-${Math.random()}`
  return prisma.tenant.create({ data: { id, name: `Tenant ${id}` } })
}

async function createPatient(tenantId: string) {
  const suffix = Math.random().toString(36).slice(2, 7)
  return prisma.patient.create({
    data: {
      tenantId,
      firstName: `John-${suffix}`,
      lastName: `Doe-${suffix}`,
    },
  })
}

describe('CaseSheet Extension', () => {
  let tenantId: string

  beforeAll(async () => {
    const tenant = await createTenant()
    tenantId = tenant.id
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  it('creates a case sheet with 32 teeth and prevents duplicates', async () => {
    const patient = await createPatient(tenantId)

    const caseSheet = await prisma.caseSheet.createCaseSheetWithTeeth(patient.id, tenantId)
    expect(caseSheet.id).toBeTruthy()
    expect(caseSheet.status).toBe(CaseSheetStatus.ACTIVE)
    expect(caseSheet.teeth.length).toBe(32)

    await expect(
      prisma.caseSheet.createCaseSheetWithTeeth(patient.id, tenantId)
    ).rejects.toThrowError()
  })

  it('updates case sheet status and retrieves with dental chart', async () => {
    const patient = await createPatient(tenantId)

    const cs = await prisma.caseSheet.createCaseSheetWithTeeth(patient.id, tenantId)
    const updated = await prisma.caseSheet.updateStatus(cs.id, CaseSheetStatus.INACTIVE)
    expect(updated.status).toBe(CaseSheetStatus.INACTIVE)

    const fetched = await prisma.caseSheet.findWithDentalChart(cs.id)
    expect(fetched?.id).toBe(cs.id)
    expect(fetched?.teeth?.length).toBe(32)
    // Ensure ordered by toothNumber asc
    const numbers = (fetched?.teeth ?? []).map(t => t.toothNumber)
    const sorted = [...numbers].sort((a, b) => a - b)
    expect(numbers).toEqual(sorted)
  })
})
