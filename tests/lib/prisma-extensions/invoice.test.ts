import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { PrismaClient, InvoiceStatus } from '@prisma/client'
import { applyAllExtensions } from '@/lib/prisma-extensions'
import { getPrismaWithExtensions } from './_setup-prisma-extensions'

const prisma = getPrismaWithExtensions()

async function createTenant() {
  const id = `tenant-${Date.now()}-${Math.random()}`
  return prisma.tenant.create({ data: { id, name: `Tenant ${id}` } })
}

async function createPatient(tenantId: string) {
  const suffix = Math.random().toString(36).slice(2, 7)
  return prisma.patient.create({ data: { tenantId, firstName: `Bill-${suffix}`, lastName: `Ing-${suffix}` } })
}

describe('Invoice Extension', () => {
  let tenantId: string
  let patientId: number

  beforeAll(async () => {
    const t = await createTenant()
    tenantId = t.id
    const p = await createPatient(tenantId)
    patientId = p.id
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  it('creates invoice with generated serial and updates amounts/status', async () => {
    const inv = await (prisma as any).invoice.createInvoice({ tenantId, patientId, invoiceDate: new Date(), totalAmount: 0 as any })
    expect(inv.serial).toMatch(/^\d+$/)
    expect(inv.status).toBe(InvoiceStatus.DRAFT)

    const updated = await prisma.invoice.updateInvoiceAmount(inv.id, 300)
    expect(Number(updated.totalAmount)).toBe(300)
    expect(Number(updated.balanceDue)).toBe(300)

    const paid = await prisma.invoice.updateAmountPaid(inv.id, 300)
    expect(Number(paid.amountPaid)).toBe(300)
    expect(Number(paid.balanceDue)).toBe(0)
    expect(paid.status).toBe(InvoiceStatus.PAID)

    await expect(prisma.invoice.updateInvoiceStatus(inv.id, InvoiceStatus.SENT)).rejects.toThrowError()
  })

  it('validates invoice data', async () => {
    const res = await prisma.invoice.validateInvoice({})
    expect(res.isValid).toBe(false)
    expect(res.errors.length).toBeGreaterThan(0)
  })
})
