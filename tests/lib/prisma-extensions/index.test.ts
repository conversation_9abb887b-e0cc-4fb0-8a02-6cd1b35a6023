import { describe, it, expect, afterAll } from 'vitest'
import { PrismaClient } from '@prisma/client'
import { applyAllExtensions, validateExtensionDependencies, defaultExtensionConfig } from '@/lib/prisma-extensions'

const base = new PrismaClient()
const prisma = applyAllExtensions(base)

describe('Prisma Extensions Index', () => {
  afterAll(async () => {
    await prisma.$disconnect()
  })

  it('applies all extensions and validates dependencies', async () => {
    expect(prisma).toBeTruthy()
    const errors = validateExtensionDependencies(defaultExtensionConfig)
    expect(errors.length).toBe(0)
  })
})
