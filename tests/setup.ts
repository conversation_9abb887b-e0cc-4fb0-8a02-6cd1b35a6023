import { beforeAll, afterAll, vi } from 'vitest';
import { execSync } from 'child_process';
import '@testing-library/jest-dom';

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'file:./tests.db';
process.env.JWT_SECRET = 'test-secret-key-for-testing-only';

// Mock ResizeObserver for UI components
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock window.matchMedia for responsive components
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

beforeAll(async () => {
  // Ensure fresh SQLite DB for tests
  try {
    execSync('rm -f prisma/schema/tests.db', { stdio: 'inherit' });
  } catch {}

  // Generate Prisma client
  execSync('pnpm prisma generate --schema=prisma/schema', { stdio: 'inherit' });
  
  // Push database schema
  execSync('pnpm prisma db push --schema=prisma/schema', { stdio: 'inherit' });
});

afterAll(async () => {
  // Clean up test database
  try {
    execSync('rm -f prisma/schema/tests.db', { stdio: 'inherit' });
  } catch {}
});
