import { describe, it, expect } from 'vitest'
import { NextRequest } from 'next/server'
import { POST } from '@/app/api/clinical/findings/[id]/treatments/route'
import prisma from '@/lib/prisma'
import { ClinicalWorkflowService } from '@/lib/clinical/workflow-service'

async function createTenant(suffix: string = '') {
  return prisma.tenant.create({ data: { name: `API Findings Treatments${suffix}` } })
}

function uniquePhone() { return `${Date.now()}${Math.floor(Math.random()*1000)}` }

async function seedFinding(tenantId: string) {
  const svc = new ClinicalWorkflowService()
  const { patient } = await svc.createPatientWithCompleteWorkflow({ firstName: 'Findy', lastName: 'Case', phoneNumber: uniquePhone(), tenantId })
  const cs = await prisma.caseSheet.findFirst({ where: { patientId: patient!.id }, include: { teeth: true } })
  const toothId = cs!.teeth[0].id
  const { finding } = await svc.createFindingWithTreatment({ toothId, description: 'Caries' })
  return finding
}

describe('/api/clinical/findings/[id]/treatments', () => {
  it('POST creates treatment and updates/creates invoice', async () => {
    const tenant = await createTenant('-success')
    const finding = await seedFinding(tenant.id)

    const req = new NextRequest(`http://localhost:3000/api/clinical/findings/${finding.id}/treatments`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ procedureName: 'Root canal', cost: 500 }),
    })

    const res = await POST(req, { params: { id: String(finding.id) } })
    const data = await res.json()

    expect(res.status).toBe(201)
    expect(data.success).toBe(true)
    expect(data.treatment?.id).toBeTruthy()
    expect(data.invoice?.id).toBeTruthy()
  })

  it('POST validates required fields', async () => {
    const req = new NextRequest('http://localhost:3000/api/clinical/findings/0/treatments', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ procedureName: '', cost: '' }),
    })

    const res = await POST(req, { params: { id: '0' } })
    expect(res.status).toBe(400)
  })
})
