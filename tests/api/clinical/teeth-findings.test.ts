import { describe, it, expect } from 'vitest'
import { NextRequest } from 'next/server'
import { POST } from '@/app/api/clinical/teeth/[id]/findings/route'
import prisma from '@/lib/prisma'
import { ClinicalWorkflowService } from '@/lib/clinical/workflow-service'

async function createTenant(suffix: string = '') {
  return prisma.tenant.create({ data: { name: `API Teeth Findings${suffix}` } })
}

function uniquePhone() { return `${Date.now()}${Math.floor(Math.random()*1000)}` }

async function createPatientWithChart(tenantId: string) {
  const svc = new ClinicalWorkflowService()
  const { patient } = await svc.createPatientWithCompleteWorkflow({ firstName: 'Toothy', lastName: 'McTooth', phoneNumber: uniquePhone(), tenantId })
  return patient!
}

async function getAnyToothIdForPatient(patientId: number) {
  const cs = await prisma.caseSheet.findFirst({ where: { patientId }, include: { teeth: true } })
  if (!cs || cs.teeth.length === 0) throw new Error('No teeth')
  return cs.teeth[0].id
}

describe('/api/clinical/teeth/[id]/findings', () => {
  it('POST creates finding only', async () => {
    const tenant = await createTenant('-finding-only')
    const patient = await createPatientWithChart(tenant.id)
    const toothId = await getAnyToothIdForPatient(patient.id)

    const req = new NextRequest(`http://localhost:3000/api/clinical/teeth/${toothId}/findings`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ description: 'Caries present' }),
    })

    const res = await POST(req, { params: { id: String(toothId) } })
    const data = await res.json()

    expect(res.status).toBe(201)
    expect(data.success).toBe(true)
    expect(data.finding?.id).toBeTruthy()
    expect(data.treatment).toBeNull()
  })

  it('POST creates finding with inline treatment', async () => {
    const tenant = await createTenant('-finding-inline')
    const patient = await createPatientWithChart(tenant.id)
    const toothId = await getAnyToothIdForPatient(patient.id)

    const req = new NextRequest(`http://localhost:3000/api/clinical/teeth/${toothId}/findings`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ description: 'Fracture', procedureName: 'Composite', cost: 150 }),
    })

    const res = await POST(req, { params: { id: String(toothId) } })
    const data = await res.json()

    expect(res.status).toBe(201)
    expect(data.success).toBe(true)
    expect(data.finding?.id).toBeTruthy()
    expect(data.treatment?.id).toBeTruthy()
  })

  it('POST validates description and tooth id', async () => {
    const req = new NextRequest('http://localhost:3000/api/clinical/teeth/0/findings', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ description: '' }),
    })

    const res = await POST(req, { params: { id: '0' } })
    expect(res.status).toBe(400)
  })
})
