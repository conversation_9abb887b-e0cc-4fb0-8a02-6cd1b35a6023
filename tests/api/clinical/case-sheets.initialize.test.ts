import { describe, it, expect } from 'vitest'
import { NextRequest } from 'next/server'
import { POST } from '@/app/api/clinical/case-sheets/[id]/initialize/route'
import prisma from '@/lib/prisma'

async function createTenant(suffix: string = '') {
  return prisma.tenant.create({ data: { name: `API CaseSheet Init${suffix}` } })
}

describe('/api/clinical/case-sheets/[id]/initialize', () => {
  it('POST initializes case sheet with 32 teeth', async () => {
    const tenant = await createTenant('-success')
    const patient = await prisma.patient.create({ data: { tenantId: tenant.id, firstName: 'Case', lastName: 'Init' } })

    const req = new NextRequest(`http://localhost:3000/api/clinical/case-sheets/${patient.id}/initialize`, { method: 'POST' })
    const res = await POST(req, { params: { id: String(patient.id) } })
    const data = await res.json()

    expect(res.status).toBe(201)
    expect(data.success).toBe(true)
    expect(data.caseSheet?.id).toBeTruthy()
    expect(data.teethCount).toBe(32)
  })

  it('POST returns 400 for invalid patient id', async () => {
    const req = new NextRequest('http://localhost:3000/api/clinical/case-sheets/abc/initialize', { method: 'POST' })
    const res = await POST(req, { params: { id: 'abc' } })
    expect(res.status).toBe(400)
  })
})
