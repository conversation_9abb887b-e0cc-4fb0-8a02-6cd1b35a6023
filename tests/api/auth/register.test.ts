import { describe, it, expect, beforeEach } from 'vitest';
import { POST } from '@/app/api/auth/register/route';
import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';

describe('/api/auth/register', () => {
  beforeEach(async () => {
    // Clean up database before each test
    await prisma.$extends({}).bypassTenant(async () => {
      await prisma.user.deleteMany();
      await prisma.tenant.deleteMany();
    });
  });

  describe('POST /api/auth/register', () => {
    it('should successfully register a new clinic and admin user', async () => {
      const validRegistrationData = {
        clinic: {
          name: 'Test Dental Clinic',
          address: '123 Main St, Test City, TC 12345',
          phone: '+1234567890',
          email: '<EMAIL>',
        },
        adminUser: {
          username: 'testadmin',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          password: 'TestPassword123!',
          confirmPassword: 'TestPassword123!',
        },
      };

      const request = new NextRequest('http://localhost:3000/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validRegistrationData),
      });

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(201);
      expect(responseData.success).toBe(true);
      expect(responseData.message).toContain('Registration completed successfully');
      expect(responseData.tenantId).toBeDefined();
      expect(responseData.userId).toBeDefined();

      // Verify tenant was created
      const createdTenant = await prisma.$extends({}).bypassTenant(async () => {
        return prisma.tenant.findUnique({
          where: { id: responseData.tenantId },
        });
      });
      expect(createdTenant).toBeDefined();
      expect(createdTenant?.name).toBe('Test Dental Clinic');

      // Verify admin user was created
      const createdUser = await prisma.$extends({}).bypassTenant(async () => {
        return prisma.user.findFirst({
          where: { 
            id: parseInt(responseData.userId),
            tenantId: responseData.tenantId,
          },
        });
      });
      expect(createdUser).toBeDefined();
      expect(createdUser?.username).toBe('testadmin');
      expect(createdUser?.userType).toBe('ADMIN');
      expect(createdUser?.isActive).toBe(true);
    });

    it('should prevent duplicate clinic registration by name', async () => {
      // Create first clinic
      const firstRegistration = {
        clinic: {
          name: 'Unique Dental Clinic',
          address: '123 First St, Test City, TC 12345',
          phone: '+1234567890',
          email: '<EMAIL>',
        },
        adminUser: {
          username: 'firstadmin',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'First',
          password: 'TestPassword123!',
          confirmPassword: 'TestPassword123!',
        },
      };

      const firstRequest = new NextRequest('http://localhost:3000/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(firstRegistration),
      });

      const firstResponse = await POST(firstRequest);
      expect(firstResponse.status).toBe(201);

      // Try to create clinic with same name
      const duplicateRegistration = {
        clinic: {
          name: 'Unique Dental Clinic', // Same name
          address: '456 Second St, Test City, TC 12345',
          phone: '+1987654321',
          email: '<EMAIL>',
        },
        adminUser: {
          username: 'secondadmin',
          email: '<EMAIL>',
          firstName: 'Jane',
          lastName: 'Second',
          password: 'TestPassword123!',
          confirmPassword: 'TestPassword123!',
        },
      };

      const duplicateRequest = new NextRequest('http://localhost:3000/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(duplicateRegistration),
      });

      const duplicateResponse = await POST(duplicateRequest);
      const responseData = await duplicateResponse.json();

      expect(duplicateResponse.status).toBe(400);
      expect(responseData.success).toBe(false);
      expect(responseData.message).toContain('already exists');
    });

    it('should validate required clinic fields', async () => {
      const invalidRegistrationData = {
        clinic: {
          name: '', // Empty name
          address: 'Too short', // Too short address
          phone: '123', // Invalid phone
          email: 'invalid-email', // Invalid email
        },
        adminUser: {
          username: 'testadmin',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          password: 'TestPassword123!',
          confirmPassword: 'TestPassword123!',
        },
      };

      const request = new NextRequest('http://localhost:3000/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invalidRegistrationData),
      });

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(400);
      expect(responseData.success).toBe(false);
      expect(responseData.message).toBe('Validation failed');
      expect(responseData.errors).toBeDefined();
      expect(responseData.errors.length).toBeGreaterThan(0);
    });

    it('should validate admin user fields', async () => {
      const invalidAdminData = {
        clinic: {
          name: 'Test Dental Clinic',
          address: '123 Main St, Test City, TC 12345',
          phone: '+1234567890',
          email: '<EMAIL>',
        },
        adminUser: {
          username: 'ab', // Too short
          email: 'invalid-email', // Invalid email
          firstName: '',  // Empty first name
          lastName: '',   // Empty last name
          password: '123', // Weak password
          confirmPassword: '456', // Passwords don't match
        },
      };

      const request = new NextRequest('http://localhost:3000/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invalidAdminData),
      });

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(400);
      expect(responseData.success).toBe(false);
      expect(responseData.message).toBe('Validation failed');
      expect(responseData.errors).toBeDefined();
      expect(responseData.errors.length).toBeGreaterThan(0);
    });

    it('should handle missing request body', async () => {
      const request = new NextRequest('http://localhost:3000/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({}),
      });

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(400);
      expect(responseData.success).toBe(false);
      expect(responseData.message).toBe('Validation failed');
    });

    it('should handle invalid JSON', async () => {
      const request = new NextRequest('http://localhost:3000/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: 'invalid json',
      });

      const response = await POST(request);
      const responseData = await response.json();

      expect(response.status).toBe(500);
      expect(responseData.success).toBe(false);
      expect(responseData.message).toContain('unexpected error');
    });

    it('should prevent username conflicts globally across all tenants', async () => {
      // Create first clinic and user
      const firstRegistration = {
        clinic: {
          name: 'First Dental Clinic',
          address: '123 First St, Test City, TC 12345',
          phone: '+1234567890',
          email: '<EMAIL>',
        },
        adminUser: {
          username: 'sharedusername',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'First',
          password: 'TestPassword123!',
          confirmPassword: 'TestPassword123!',
        },
      };

      const firstRequest = new NextRequest('http://localhost:3000/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(firstRegistration),
      });

      const firstResponse = await POST(firstRequest);
      expect(firstResponse.status).toBe(201);

      // This should fail since username is globally unique
      const secondRegistration = {
        clinic: {
          name: 'Second Dental Clinic',
          address: '456 Second St, Test City, TC 12345',
          phone: '+1987654321',
          email: '<EMAIL>',
        },
        adminUser: {
          username: 'sharedusername', // Same username - should fail
          email: '<EMAIL>',
          firstName: 'Jane',
          lastName: 'Second',
          password: 'TestPassword123!',
          confirmPassword: 'TestPassword123!',
        },
      };

      const secondRequest = new NextRequest('http://localhost:3000/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(secondRegistration),
      });

      const secondResponse = await POST(secondRequest);
      const responseData = await secondResponse.json();

      expect(secondResponse.status).toBe(400);
      expect(responseData.success).toBe(false);
      expect(responseData.message).toContain('Failed to create admin user');
    });
  });
});
