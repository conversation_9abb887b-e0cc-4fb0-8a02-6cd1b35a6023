import { describe, it, expect, beforeEach, vi } from 'vitest'
import { NextRequest } from 'next/server'
import { POST } from '@/app/api/patients/create-with-workflow/route'
import prisma from '@/lib/prisma'

// Mock AuthService to bypass authentication
vi.mock('@/lib/auth/services/auth-service', () => ({
  AuthService: {
    validateSession: vi.fn().mockResolvedValue({
      isValid: true,
      session: {
        userId: 'test-user-123',
        tenantId: 'test-tenant-456',
        username: 'testuser',
        userType: 'DENTIST',
        isActive: true,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 86400000),
      },
      message: 'Session is valid',
    }),
  },
}))

// Mock TenantContextManager
vi.mock('@/lib/tenant-context', () => ({
  TenantContextManager: {
    createContextFromAuthSession: vi.fn().mockReturnValue({
      tenantId: 'test-tenant-456',
      userId: 'test-user-123',
      userRole: 'DENTIST',
      isAuthenticated: true,
    }),
    setGlobalContext: vi.fn(),
    clearGlobalContext: vi.fn(),
    getCurrentContext: vi.fn(),
    getCurrentTenantId: vi.fn().mockReturnValue('test-tenant-456'),
  },
}))

async function createTenant(suffix: string = '') {
  return prisma.tenant.create({ data: { name: `API Patients Create${suffix}` } })
}

describe('/api/patients/create-with-workflow', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('POST creates patient, case sheet, and 32 teeth', async () => {
    const tenant = await createTenant('-success')

    const body = {
      firstName: 'Alice',
      lastName: 'Wonder',
      phoneNumber: '**********',
      tenantId: tenant.id,
      email: '<EMAIL>',
    }

    const req = new NextRequest('http://localhost:3000/api/patients/create-with-workflow', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    })

    const res = await POST(req)
    const data = await res.json()

    expect(res.status).toBe(201)
    expect(data.success).toBe(true)
    expect(data.patient?.id).toBeTruthy()
    expect(data.caseSheet?.id).toBeTruthy()
    expect(data.teethCount).toBe(32)

    const teethCount = await prisma.tooth.count({ where: { caseSheetId: data.caseSheet.id } })
    expect(teethCount).toBe(32)
  })

  it('POST validates required fields and returns 400', async () => {
    const req = new NextRequest('http://localhost:3000/api/patients/create-with-workflow', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({}),
    })

    const res = await POST(req)
    const data = await res.json()

    expect(res.status).toBe(400)
    expect(data.success).toBe(false)
    expect(Array.isArray(data.errors)).toBe(true)
    expect(data.errors).toEqual(expect.arrayContaining([
      'firstName is required',
      'lastName is required',
      'phoneNumber is required',
      'tenantId is required',
    ]))
  })
})
