import { describe, it, expect } from 'vitest'
import { NextRequest } from 'next/server'
import { POST } from '@/app/api/patients/create-with-workflow/route'
import prisma from '@/lib/prisma'

async function createTenant(suffix: string = '') {
  return prisma.tenant.create({ data: { name: `API Patients Create${suffix}` } })
}

describe('/api/patients/create-with-workflow', () => {
  it('POST creates patient, case sheet, and 32 teeth', async () => {
    const tenant = await createTenant('-success')

    const body = {
      firstName: 'Alice',
      lastName: 'Wonder',
      phoneNumber: '**********',
      tenantId: tenant.id,
      email: '<EMAIL>',
    }

    const req = new NextRequest('http://localhost:3000/api/patients/create-with-workflow', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    })

    const res = await POST(req)
    const data = await res.json()

    expect(res.status).toBe(201)
    expect(data.success).toBe(true)
    expect(data.patient?.id).toBeTruthy()
    expect(data.caseSheet?.id).toBeTruthy()
    expect(data.teethCount).toBe(32)

    const teethCount = await prisma.tooth.count({ where: { caseSheetId: data.caseSheet.id } })
    expect(teethCount).toBe(32)
  })

  it('POST validates required fields and returns 400', async () => {
    const req = new NextRequest('http://localhost:3000/api/patients/create-with-workflow', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({}),
    })

    const res = await POST(req)
    const data = await res.json()

    expect(res.status).toBe(400)
    expect(data.success).toBe(false)
    expect(Array.isArray(data.errors)).toBe(true)
    expect(data.errors).toEqual(expect.arrayContaining([
      'firstName is required',
      'lastName is required',
      'phoneNumber is required',
      'tenantId is required',
    ]))
  })
})
