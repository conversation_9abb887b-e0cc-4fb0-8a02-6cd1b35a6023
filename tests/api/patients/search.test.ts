import { describe, it, expect } from 'vitest'
import { NextRequest } from 'next/server'
import { POST } from '@/app/api/patients/search/route'
import prisma from '@/lib/prisma'

async function createTenant(suffix: string = '') {
  return prisma.tenant.create({ data: { name: `API Patients Search${suffix}` } })
}

async function createPatientInTenant(tenantId: string) {
  return prisma.patient.create({
    data: {
      tenantId,
      firstName: 'Bob',
      lastName: 'Builder',
      phoneNumber: '**********',
    },
  })
}

describe('/api/patients/search', () => {
  it('POST searches by patientId and returns patient with case sheet info', async () => {
    const tenant = await createTenant('-by-id')
    const patient = await createPatientInTenant(tenant.id)

    const req = new NextRequest('http://localhost:3000/api/patients/search', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ tenantId: tenant.id, patientId: patient.id }),
    })

    const res = await POST(req)
    const data = await res.json()

    expect(res.status).toBe(200)
    expect(data.success).toBe(true)
    expect(Array.isArray(data.patients)).toBe(true)
    expect(data.patients.length).toBe(1)
    expect(data.patients[0].id).toBe(patient.id)
    expect(data.patients[0]).toHaveProperty('caseSheetId')
    expect(data.patients[0]).toHaveProperty('caseSheetStatus')
  })

  it('POST searches by phoneNumber', async () => {
    const tenant = await createTenant('-by-phone')
    const patient = await createPatientInTenant(tenant.id)

    const req = new NextRequest('http://localhost:3000/api/patients/search', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ tenantId: tenant.id, phoneNumber: patient.phoneNumber }),
    })

    const res = await POST(req)
    const data = await res.json()

    expect(res.status).toBe(200)
    expect(data.success).toBe(true)
    expect(data.patients.length).toBe(1)
    expect(data.patients[0].id).toBe(patient.id)
  })

  it('POST searches by name/searchTerm returns empty when no match', async () => {
    const tenant = await createTenant('-by-name')

    const req = new NextRequest('http://localhost:3000/api/patients/search', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ tenantId: tenant.id, searchTerm: 'NoSuch Person' }),
    })

    const res = await POST(req)
    const data = await res.json()

    expect(res.status).toBe(200)
    expect(data.success).toBe(true)
    expect(data.patients.length).toBe(0)
  })

  it('POST returns 400 when missing tenantId and search parameters', async () => {
    const req = new NextRequest('http://localhost:3000/api/patients/search', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({}),
    })

    const res = await POST(req)
    const data = await res.json()

    expect(res.status).toBe(400)
    expect(data.success).toBe(false)
    expect(Array.isArray(data.errors)).toBe(true)
  })
})
