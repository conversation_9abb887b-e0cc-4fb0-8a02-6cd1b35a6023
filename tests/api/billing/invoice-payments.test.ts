import { describe, it, expect } from 'vitest'
import { NextRequest } from 'next/server'
import { POST } from '@/app/api/billing/invoices/[id]/payments/route'
import prisma from '@/lib/prisma'
import { ClinicalWorkflowService } from '@/lib/clinical/workflow-service'

async function createTenant(suffix: string = '') {
  return prisma.tenant.create({ data: { name: `API Invoice Payments${suffix}` } })
}

function uniquePhone() { return `${Date.now()}${Math.floor(Math.random()*1000)}` }

async function seedPatientWithInvoice(tenantId: string) {
  const svc = new ClinicalWorkflowService()
  const { patient } = await svc.createPatientWithCompleteWorkflow({ firstName: 'Invo', lastName: 'Ice', phoneNumber: uniquePhone(), tenantId })
  const cs = await prisma.caseSheet.findFirst({ where: { patientId: patient!.id }, include: { teeth: true } })
  const toothId = cs!.teeth[0].id
  const { finding } = await svc.createFindingWithTreatment({ toothId, description: 'Caries' })
  const { invoice } = await svc.createTreatmentWithInvoice({ findingId: finding.id, procedureName: 'Filling', cost: 200 })
  return { patient: patient!, invoice }
}

describe('/api/billing/invoices/[id]/payments', () => {
  it('POST records payment for specified invoice and updates balances', async () => {
    const tenant = await createTenant('-with-invoice')
    const { patient, invoice } = await seedPatientWithInvoice(tenant.id)

    const req = new NextRequest(`http://localhost:3000/api/billing/invoices/${invoice.id}/payments`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ patientId: patient.id, amount: 50, paymentMethod: 'CASH' }),
    })

    const res = await POST(req, { params: { id: String(invoice.id) } })
    const data = await res.json()

    expect(res.status).toBe(201)
    expect(data.success).toBe(true)
    expect(data.payment?.id).toBeTruthy()
    expect(data.invoice?.id).toBeTruthy()
  })

  it('POST records payment without specifying invoice id (auto-select)', async () => {
    const tenant = await createTenant('-auto')
    const { patient } = await seedPatientWithInvoice(tenant.id)

    const req = new NextRequest('http://localhost:3000/api/billing/invoices/invalid/payments', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ patientId: patient.id, amount: 200, paymentMethod: 'CARD' }),
    })

    const res = await POST(req, { params: { id: 'invalid' } })
    const data = await res.json()

    expect(res.status).toBe(201)
    expect(data.success).toBe(true)
    expect(data.payment?.id).toBeTruthy()
  })

  it('POST validates amount and paymentMethod', async () => {
    const req = new NextRequest('http://localhost:3000/api/billing/invoices/0/payments', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ patientId: 1, amount: '', paymentMethod: 'INVALID' }),
    })

    const res = await POST(req, { params: { id: '0' } })
    const data = await res.json()

    expect(res.status).toBe(400)
    expect(data.success).toBe(false)
    expect(Array.isArray(data.errors)).toBe(true)
  })
})
