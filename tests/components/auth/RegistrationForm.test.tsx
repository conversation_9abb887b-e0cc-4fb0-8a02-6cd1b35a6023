import React from "react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { RegistrationForm } from "@/components/auth/RegistrationForm";
import type { RegistrationData } from "@/lib/auth/schemas";

// Mock the Lucide React icons
vi.mock("lucide-react", () => ({
  AlertCircle: () => <div data-testid="alert-circle" />,
  Building2: () => <div data-testid="building2" />,
  User: () => <div data-testid="user" />,
  Lock: () => <div data-testid="lock" />,
  Eye: () => <div data-testid="eye" />,
  EyeOff: () => <div data-testid="eye-off" />,
}));

describe("RegistrationForm", () => {
  const mockOnSubmit = vi.fn();
  
  const validFormData = {
    clinic: {
      name: "Test Dental Clinic",
      address: "123 Main Street, City, State 12345",
      phone: "5551234567",
      email: "<EMAIL>",
    },
    adminUser: {
      username: "testadmin",
      email: "<EMAIL>",
      firstName: "John",
      lastName: "Doe",
      password: "TestPassword123!",
      confirmPassword: "TestPassword123!",
    },
  };

  beforeEach(() => {
    mockOnSubmit.mockClear();
  });

  describe("Rendering", () => {
    it("renders all form sections and fields", () => {
      render(<RegistrationForm onSubmit={mockOnSubmit} />);

      // Check main title and description
      expect(screen.getByText("Register Your Clinic")).toBeInTheDocument();
      expect(screen.getByText("Create your clinic account and set up your administrative user")).toBeInTheDocument();

      // Check clinic information section
      expect(screen.getByText("Clinic Information")).toBeInTheDocument();
      expect(screen.getByLabelText("Clinic Name")).toBeInTheDocument();
      expect(screen.getByLabelText("Address")).toBeInTheDocument();
      expect(screen.getByLabelText("Phone Number")).toBeInTheDocument();
      expect(screen.getByLabelText("Clinic Email")).toBeInTheDocument();
      expect(screen.getByLabelText("Admin Email")).toBeInTheDocument();

      // Check admin user section
      expect(screen.getByText("Administrator Account")).toBeInTheDocument();
      expect(screen.getByLabelText("First Name")).toBeInTheDocument();
      expect(screen.getByLabelText("Last Name")).toBeInTheDocument();
      expect(screen.getByLabelText("Username")).toBeInTheDocument();
      // Check that we have password fields (using placeholders since form structure is complex)
      expect(screen.getByPlaceholderText("Enter password")).toBeInTheDocument();
      expect(screen.getByPlaceholderText("Confirm password")).toBeInTheDocument();

      // Check submit button
      expect(screen.getByRole("button", { name: "Register Clinic" })).toBeInTheDocument();
    });

    it("shows loading state when isLoading is true", () => {
      render(<RegistrationForm onSubmit={mockOnSubmit} isLoading={true} />);

      // Check loading button text
      expect(screen.getByRole("button", { name: "Creating Account..." })).toBeInTheDocument();
      expect(screen.getByRole("button", { name: "Creating Account..." })).toBeDisabled();

      // Check that inputs are disabled
      expect(screen.getByLabelText("Clinic Name")).toBeDisabled();
      expect(screen.getByLabelText("Username")).toBeDisabled();
    });

    it("displays API error when provided", () => {
      const apiError = "Server error occurred";
      render(<RegistrationForm onSubmit={mockOnSubmit} apiError={apiError} />);

      expect(screen.getByText(apiError)).toBeInTheDocument();
    });

    it("displays validation errors when provided", () => {
      const errors = ["Error 1", "Error 2"];
      render(<RegistrationForm onSubmit={mockOnSubmit} errors={errors} />);

      expect(screen.getByText("Error 1")).toBeInTheDocument();
      expect(screen.getByText("Error 2")).toBeInTheDocument();
    });
  });

  describe("Form Validation", () => {
    it("shows validation errors for empty required fields", async () => {
      const user = userEvent.setup();
      render(<RegistrationForm onSubmit={mockOnSubmit} />);

      const submitButton = screen.getByRole("button", { name: "Register Clinic" });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText("Clinic name must be at least 2 characters long")).toBeInTheDocument();
        expect(screen.getByText("Address must be at least 5 characters long")).toBeInTheDocument();
        expect(screen.getByText("First name is required")).toBeInTheDocument();
        expect(screen.getByText("Last name is required")).toBeInTheDocument();
        expect(screen.getByText("Username must be at least 3 characters long")).toBeInTheDocument();
        expect(screen.getByText("Password must be at least 8 characters long")).toBeInTheDocument();
      });

      expect(mockOnSubmit).not.toHaveBeenCalled();
    });

    it("validates email format", async () => {
      const user = userEvent.setup();
      render(<RegistrationForm onSubmit={mockOnSubmit} />);

      // Only enter invalid email to isolate the email validation
      const clinicEmailInput = screen.getByLabelText("Clinic Email");
      await user.type(clinicEmailInput, "invalid-email");

      const submitButton = screen.getByRole("button", { name: "Register Clinic" });
      await user.click(submitButton);

      await waitFor(() => {
        // Check for any form validation error messages that appear
        const errorMessages = screen.getAllByRole("textbox", { invalid: true });
        expect(errorMessages.length).toBeGreaterThan(0);
      });

      // The form should not submit with invalid data
      expect(mockOnSubmit).not.toHaveBeenCalled();
    });

    it("validates phone number format", async () => {
      const user = userEvent.setup();
      render(<RegistrationForm onSubmit={mockOnSubmit} />);

      const phoneInput = screen.getByLabelText("Phone Number");
      await user.type(phoneInput, "123");

      const submitButton = screen.getByRole("button", { name: "Register Clinic" });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText("Phone number must be at least 10 digits")).toBeInTheDocument();
      });
    });

    it("validates password strength requirements", async () => {
      const user = userEvent.setup();
      render(<RegistrationForm onSubmit={mockOnSubmit} />);

      const passwordInput = screen.getByPlaceholderText("Enter password");
      await user.type(passwordInput, "weakpassword");

      const submitButton = screen.getByRole("button", { name: "Register Clinic" });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText("Password must contain at least one uppercase letter")).toBeInTheDocument();
      });
    });

    it("validates password confirmation match", async () => {
      const user = userEvent.setup();
      render(<RegistrationForm onSubmit={mockOnSubmit} />);

      const passwordInput = screen.getByPlaceholderText("Enter password");
      const confirmPasswordInput = screen.getByPlaceholderText("Confirm password");

      await user.type(passwordInput, "TestPassword123!");
      await user.type(confirmPasswordInput, "DifferentPassword123!");

      const submitButton = screen.getByRole("button", { name: "Register Clinic" });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText("Passwords don't match")).toBeInTheDocument();
      });
    });

    it("validates username format", async () => {
      const user = userEvent.setup();
      render(<RegistrationForm onSubmit={mockOnSubmit} />);

      const usernameInput = screen.getByLabelText("Username");
      await user.type(usernameInput, "ab"); // Too short

      const submitButton = screen.getByRole("button", { name: "Register Clinic" });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText("Username must be at least 3 characters long")).toBeInTheDocument();
      });
    });
  });

  describe("Password Strength Indicator", () => {
    it("shows password strength indicator", async () => {
      const user = userEvent.setup();
      render(<RegistrationForm onSubmit={mockOnSubmit} />);

      const passwordInput = screen.getByPlaceholderText("Enter password");

      // Test weak password
      await user.type(passwordInput, "abc");
      expect(screen.getByText("Password strength: weak")).toBeInTheDocument();

      // Test strong password
      await user.clear(passwordInput);
      await user.type(passwordInput, "TestPassword123!");
      expect(screen.getByText("Password strength: strong")).toBeInTheDocument();
    });
  });

  describe("Password Visibility Toggle", () => {
    it("toggles password visibility", async () => {
      const user = userEvent.setup();
      render(<RegistrationForm onSubmit={mockOnSubmit} />);

      const passwordInput = screen.getByPlaceholderText("Enter password");
      const toggleButton = screen.getAllByRole("button").find(button => 
        button.querySelector('[data-testid="eye"]')
      );

      // Initially password should be hidden
      expect(passwordInput).toHaveAttribute("type", "password");

      // Click to show password
      if (toggleButton) {
        await user.click(toggleButton);
        expect(passwordInput).toHaveAttribute("type", "text");

        // Click to hide password again
        await user.click(toggleButton);
        expect(passwordInput).toHaveAttribute("type", "password");
      }
    });

    it("toggles confirm password visibility", async () => {
      const user = userEvent.setup();
      render(<RegistrationForm onSubmit={mockOnSubmit} />);

      const confirmPasswordInput = screen.getByPlaceholderText("Confirm password");
      const toggleButtons = screen.getAllByRole("button").filter(button => 
        button.querySelector('[data-testid="eye"]')
      );
      const confirmPasswordToggle = toggleButtons[1]; // Second toggle button

      // Initially password should be hidden
      expect(confirmPasswordInput).toHaveAttribute("type", "password");

      // Click to show password
      if (confirmPasswordToggle) {
        await user.click(confirmPasswordToggle);
        expect(confirmPasswordInput).toHaveAttribute("type", "text");
      }
    });
  });

  describe("Form Submission", () => {
    it("submits form with valid data", async () => {
      const user = userEvent.setup();
      render(<RegistrationForm onSubmit={mockOnSubmit} />);

      // Fill out clinic information
      await user.type(screen.getByLabelText("Clinic Name"), validFormData.clinic.name);
      await user.type(screen.getByLabelText("Address"), validFormData.clinic.address);
      await user.type(screen.getByLabelText("Phone Number"), validFormData.clinic.phone);
                    // Get the clinic email field by placeholder
              const clinicEmailInput = screen.getByPlaceholderText("<EMAIL>");
              await user.type(clinicEmailInput, validFormData.clinic.email);

              // Fill out admin user information
              await user.type(screen.getByLabelText("First Name"), validFormData.adminUser.firstName);
              await user.type(screen.getByLabelText("Last Name"), validFormData.adminUser.lastName);
              await user.type(screen.getByLabelText("Username"), validFormData.adminUser.username);
              
              // Get the admin email field by placeholder
              const adminEmailInput = screen.getByPlaceholderText("<EMAIL>");
              await user.type(adminEmailInput, validFormData.adminUser.email);
      
      await user.type(screen.getByPlaceholderText("Enter password"), validFormData.adminUser.password);
      await user.type(screen.getByPlaceholderText("Confirm password"), validFormData.adminUser.confirmPassword);

      const submitButton = screen.getByRole("button", { name: "Register Clinic" });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith(validFormData);
      });
    });

    it("handles submission errors gracefully", async () => {
      const user = userEvent.setup();
      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});
      
      mockOnSubmit.mockRejectedValue(new Error("Submission failed"));
      
      render(<RegistrationForm onSubmit={mockOnSubmit} />);

      // Fill out valid form data
      await user.type(screen.getByLabelText("Clinic Name"), validFormData.clinic.name);
      await user.type(screen.getByLabelText("Address"), validFormData.clinic.address);
      await user.type(screen.getByLabelText("Phone Number"), validFormData.clinic.phone);
      
      // Get email fields by placeholder
      const clinicEmailInput = screen.getByPlaceholderText("<EMAIL>");
      await user.type(clinicEmailInput, validFormData.clinic.email);
      
      await user.type(screen.getByLabelText("First Name"), validFormData.adminUser.firstName);
      await user.type(screen.getByLabelText("Last Name"), validFormData.adminUser.lastName);
      await user.type(screen.getByLabelText("Username"), validFormData.adminUser.username);
      
      const adminEmailInput = screen.getByPlaceholderText("<EMAIL>");
      await user.type(adminEmailInput, validFormData.adminUser.email);
      
      await user.type(screen.getByPlaceholderText("Enter password"), validFormData.adminUser.password);
      await user.type(screen.getByPlaceholderText("Confirm password"), validFormData.adminUser.confirmPassword);

      const submitButton = screen.getByRole("button", { name: "Register Clinic" });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalled();
        expect(consoleSpy).toHaveBeenCalledWith("Registration form submission error:", expect.any(Error));
      });

      consoleSpy.mockRestore();
    });

    it("prevents submission when form is loading", async () => {
      const user = userEvent.setup();
      render(<RegistrationForm onSubmit={mockOnSubmit} isLoading={true} />);

      const submitButton = screen.getByRole("button", { name: "Creating Account..." });
      await user.click(submitButton);

      // Should not call onSubmit when disabled
      expect(mockOnSubmit).not.toHaveBeenCalled();
    });
  });

  describe("Accessibility", () => {
    it("has proper form labels and ARIA attributes", () => {
      render(<RegistrationForm onSubmit={mockOnSubmit} />);

      // Check that all inputs have proper labels
      const inputs = screen.getAllByRole("textbox");
      inputs.forEach(input => {
        expect(input).toHaveAccessibleName();
      });

      // Check password inputs using placeholders since they're in complex nested form structure
      const passwordInput = screen.getByPlaceholderText("Enter password");
      const confirmPasswordInput = screen.getByPlaceholderText("Confirm password");
      expect(passwordInput).toBeInTheDocument();
      expect(confirmPasswordInput).toBeInTheDocument();
    });

    it("has proper heading structure", () => {
      render(<RegistrationForm onSubmit={mockOnSubmit} />);

      expect(screen.getByRole("heading", { level: 1, name: "Register Your Clinic" })).toBeInTheDocument();
    });
  });
});
