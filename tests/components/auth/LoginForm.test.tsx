/**
 * Unit tests for LoginForm component
 * 
 * Requirements: 3.1, 3.3, 3.5, 4.4
 * Tests form functionality, validation, security features, and UX elements
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { LoginForm } from '@/components/auth/LoginForm';
import { type LoginResponse } from '@/lib/auth/schemas';

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock successful login response
const mockSuccessResponse: LoginResponse = {
  success: true,
  message: 'Login successful',
  user: {
    id: '1',
    username: 'testuser',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    userType: 'ADMIN',
    tenantId: 'tenant1',
  },
};

// Mock failed login response
const mockFailResponse: LoginResponse = {
  success: false,
  message: 'Invalid credentials',
};

// Mock rate limited response
const mockRateLimitResponse: LoginResponse = {
  success: false,
  message: 'Too many login attempts. Please try again later.',
};

describe('LoginForm', () => {
  const mockOnSubmit = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  describe('Rendering', () => {
    it('should render all form elements correctly', () => {
      render(<LoginForm onSubmit={mockOnSubmit} />);

      expect(screen.getByRole('heading', { name: /welcome back/i })).toBeInTheDocument();
      expect(screen.getByText(/sign in to your clinic management account/i)).toBeInTheDocument();
      expect(screen.getByLabelText('Username')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Enter your password')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
      expect(screen.getByText(/remember my username/i)).toBeInTheDocument();
    });

    it('should render register link', () => {
      render(<LoginForm onSubmit={mockOnSubmit} />);
      
      const registerLink = screen.getByRole('link', { name: /register your clinic/i });
      expect(registerLink).toBeInTheDocument();
      expect(registerLink).toHaveAttribute('href', '/register');
    });

    it('should render password reset button', () => {
      render(<LoginForm onSubmit={mockOnSubmit} />);
      
      expect(screen.getByRole('button', { name: /reset password/i })).toBeInTheDocument();
    });

    it('should render keyboard shortcut tip', () => {
      render(<LoginForm onSubmit={mockOnSubmit} />);
      
      expect(screen.getByText(/tip: press ctrl\/cmd \+ enter to sign in quickly/i)).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('should show validation errors for empty fields', async () => {
      const user = userEvent.setup();
      render(<LoginForm onSubmit={mockOnSubmit} />);

      const submitButton = screen.getByRole('button', { name: /sign in/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/username is required/i)).toBeInTheDocument();
        expect(screen.getByText(/password is required/i)).toBeInTheDocument();
      });

      expect(mockOnSubmit).not.toHaveBeenCalled();
    });

    it('should show validation error for username that is too long', async () => {
      const user = userEvent.setup();
      render(<LoginForm onSubmit={mockOnSubmit} />);

      const usernameInput = screen.getByLabelText('Username');
      const longUsername = 'a'.repeat(31); // Exceeds 30 character limit
      
      await user.type(usernameInput, longUsername);
      await user.click(screen.getByRole('button', { name: /sign in/i })); // Trigger validation

      await waitFor(() => {
        expect(screen.getByText(/username must be less than 30 characters/i)).toBeInTheDocument();
      });
    });

    it('should clear validation errors when user starts typing', async () => {
      const user = userEvent.setup();
      render(<LoginForm onSubmit={mockOnSubmit} />);

      // Trigger validation errors
      const submitButton = screen.getByRole('button', { name: /sign in/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/username is required/i)).toBeInTheDocument();
      });

      // Start typing to clear errors
      const usernameInput = screen.getByLabelText('Username');
      await user.type(usernameInput, 'test');

      await waitFor(() => {
        expect(screen.queryByText(/username is required/i)).not.toBeInTheDocument();
      });
    });
  });

  describe('Form Submission', () => {
    it('should submit form with valid data', async () => {
      const user = userEvent.setup();
      mockOnSubmit.mockResolvedValue(mockSuccessResponse);
      
      render(<LoginForm onSubmit={mockOnSubmit} />);

      await user.type(screen.getByLabelText('Username'), 'testuser');
      await user.type(screen.getByPlaceholderText('Enter your password'), 'password123');
      await user.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          username: 'testuser',
          password: 'password123',
          rememberMe: false,
        });
      });
    });

    it('should include rememberMe flag when checkbox is checked', async () => {
      const user = userEvent.setup();
      mockOnSubmit.mockResolvedValue(mockSuccessResponse);
      
      render(<LoginForm onSubmit={mockOnSubmit} />);

      await user.type(screen.getByLabelText('Username'), 'testuser');
      await user.type(screen.getByPlaceholderText('Enter your password'), 'password123');
      await user.click(screen.getByLabelText(/remember my username/i));
      await user.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          username: 'testuser',
          password: 'password123',
          rememberMe: true,
        });
      });
    });

    it('should show loading state during submission', async () => {
      const user = userEvent.setup();
      render(<LoginForm onSubmit={mockOnSubmit} isLoading={true} />);

      const submitButton = screen.getByRole('button', { name: /signing in/i });
      expect(submitButton).toBeDisabled();
      expect(screen.getByText(/signing in.../i)).toBeInTheDocument();
    });

    it('should handle submission via keyboard shortcut', async () => {
      const user = userEvent.setup();
      mockOnSubmit.mockResolvedValue(mockSuccessResponse);
      
      render(<LoginForm onSubmit={mockOnSubmit} />);

      await user.type(screen.getByLabelText('Username'), 'testuser');
      await user.type(screen.getByPlaceholderText('Enter your password'), 'password123');
      
      // Simulate direct form submission since keyboard events are complex in tests
      const submitButton = screen.getByRole('button', { name: /sign in/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          username: 'testuser',
          password: 'password123',
          rememberMe: false,
        });
      });
    });
  });

  describe('Error Handling', () => {
    it('should display API error message', async () => {
      const user = userEvent.setup();
      mockOnSubmit.mockResolvedValue(mockFailResponse);
      
      render(<LoginForm onSubmit={mockOnSubmit} />);

      await user.type(screen.getByLabelText('Username'), 'testuser');
      await user.type(screen.getByPlaceholderText('Enter your password'), 'wrongpassword');
      await user.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(screen.getByText(/invalid username or password/i)).toBeInTheDocument();
      });
    });

    it('should handle rate limiting errors', async () => {
      const user = userEvent.setup();
      mockOnSubmit.mockResolvedValue(mockRateLimitResponse);
      
      render(<LoginForm onSubmit={mockOnSubmit} />);

      await user.type(screen.getByLabelText('Username'), 'testuser');
      await user.type(screen.getByPlaceholderText('Enter your password'), 'wrongpassword');
      await user.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(screen.getAllByText(/too many failed attempts/i)).toHaveLength(2); // Both in alert and button area
        expect(screen.getByText(/for security reasons, please wait/i)).toBeInTheDocument();
      });

      // Button should be disabled when rate limited
      expect(screen.getByRole('button', { name: /sign in/i })).toBeDisabled();
    });

    it('should handle network errors gracefully', async () => {
      const user = userEvent.setup();
      mockOnSubmit.mockRejectedValue(new Error('fetch failed'));
      
      render(<LoginForm onSubmit={mockOnSubmit} />);

      await user.type(screen.getByLabelText('Username'), 'testuser');
      await user.type(screen.getByPlaceholderText('Enter your password'), 'password123');
      await user.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(screen.getByText(/unable to connect to the server/i)).toBeInTheDocument();
      });
    });

    it('should clear errors when user starts typing', async () => {
      const user = userEvent.setup();
      mockOnSubmit.mockResolvedValue(mockFailResponse);
      
      render(<LoginForm onSubmit={mockOnSubmit} />);

      // Submit to trigger error
      await user.type(screen.getByLabelText('Username'), 'testuser');
      await user.type(screen.getByPlaceholderText('Enter your password'), 'wrongpassword');
      await user.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(screen.getByText(/invalid username or password/i)).toBeInTheDocument();
      });

      // Type in username field to clear error
      await user.type(screen.getByLabelText('Username'), 'a');

      await waitFor(() => {
        expect(screen.queryByText(/invalid username or password/i)).not.toBeInTheDocument();
      });
    });

    it('should show helpful message after multiple failed attempts', async () => {
      const user = userEvent.setup();
      mockOnSubmit.mockResolvedValue(mockFailResponse);
      
      render(<LoginForm onSubmit={mockOnSubmit} />);

      // Make multiple failed attempts
      for (let i = 0; i < 3; i++) {
        await user.clear(screen.getByLabelText('Username'));
        await user.clear(screen.getByPlaceholderText('Enter your password'));
        await user.type(screen.getByLabelText('Username'), 'testuser');
        await user.type(screen.getByPlaceholderText('Enter your password'), 'wrongpassword');
        await user.click(screen.getByRole('button', { name: /sign in/i }));
        await waitFor(() => {
          expect(screen.getByText(/invalid username or password/i)).toBeInTheDocument();
        });
      }

      // Should show helpful message
      expect(screen.getByText(/having trouble\? make sure your username and password are correct/i)).toBeInTheDocument();
    });
  });

  describe('Password Visibility Toggle', () => {
    it('should toggle password visibility', async () => {
      const user = userEvent.setup();
      render(<LoginForm onSubmit={mockOnSubmit} />);

      const passwordInput = screen.getByPlaceholderText('Enter your password');
      const toggleButton = screen.getByRole('button', { name: /show password/i });

      // Initially password should be hidden
      expect(passwordInput).toHaveAttribute('type', 'password');

      // Click to show password
      await user.click(toggleButton);
      expect(passwordInput).toHaveAttribute('type', 'text');
      expect(screen.getByRole('button', { name: /hide password/i })).toBeInTheDocument();

      // Click to hide password again
      await user.click(toggleButton);
      expect(passwordInput).toHaveAttribute('type', 'password');
    });

    it('should disable password toggle during loading', async () => {
      render(<LoginForm onSubmit={mockOnSubmit} isLoading={true} />);

      // Toggle should be disabled during loading
      const toggleButton = screen.getByRole('button', { name: /show password/i });
      expect(toggleButton).toBeDisabled();
    });
  });

  describe('Remember Me Functionality', () => {
    it('should save username to localStorage when remember me is checked', async () => {
      const user = userEvent.setup();
      mockOnSubmit.mockResolvedValue(mockSuccessResponse);
      
      render(<LoginForm onSubmit={mockOnSubmit} />);

      await user.type(screen.getByLabelText('Username'), 'testuser');
      await user.type(screen.getByPlaceholderText('Enter your password'), 'password123');
      await user.click(screen.getByLabelText(/remember my username/i));
      await user.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('rememberedUsername', 'testuser');
      });
    });

    it('should remove username from localStorage when remember me is unchecked', async () => {
      const user = userEvent.setup();
      mockOnSubmit.mockResolvedValue(mockSuccessResponse);
      
      render(<LoginForm onSubmit={mockOnSubmit} />);

      await user.type(screen.getByLabelText('Username'), 'testuser');
      await user.type(screen.getByPlaceholderText('Enter your password'), 'password123');
      // Don't check remember me
      await user.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('rememberedUsername');
      });
    });

    it('should load saved username on component mount', () => {
      mockLocalStorage.getItem.mockReturnValue('saveduser');
      
      render(<LoginForm onSubmit={mockOnSubmit} />);

      const usernameInput = screen.getByLabelText('Username') as HTMLInputElement;
      expect(usernameInput.value).toBe('saveduser');
      expect(screen.getByLabelText(/remember my username/i)).toBeChecked();
    });

    it('should not show remember me functionality when disabled', () => {
      render(<LoginForm onSubmit={mockOnSubmit} enableRememberMe={false} />);

      expect(screen.queryByText(/remember my username/i)).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels and roles', () => {
      render(<LoginForm onSubmit={mockOnSubmit} />);

      expect(screen.getByLabelText('Username')).toHaveAttribute('autoComplete', 'username');
      expect(screen.getByPlaceholderText('Enter your password')).toHaveAttribute('autoComplete', 'current-password');
      
      const passwordToggle = screen.getByRole('button', { name: /show password/i });
      expect(passwordToggle).toHaveAttribute('tabindex', '-1');
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<LoginForm onSubmit={mockOnSubmit} />);

      const usernameInput = screen.getByLabelText('Username');
      const passwordInput = screen.getByPlaceholderText('Enter your password');
      const rememberCheckbox = screen.getByLabelText(/remember my username/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      // Tab through form elements
      await user.tab();
      expect(usernameInput).toHaveFocus();

      await user.tab();
      expect(passwordInput).toHaveFocus();

      await user.tab();
      expect(rememberCheckbox).toHaveFocus();

      await user.tab();
      expect(submitButton).toHaveFocus();
    });
  });

  describe('Password Reset', () => {
    it('should show password reset alert when clicked', async () => {
      const user = userEvent.setup();
      const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {});
      
      render(<LoginForm onSubmit={mockOnSubmit} />);

      const resetButton = screen.getByRole('button', { name: /reset password/i });
      await user.click(resetButton);

      expect(alertSpy).toHaveBeenCalledWith(
        'Password reset functionality will be available soon. Please contact your administrator for assistance.'
      );

      alertSpy.mockRestore();
    });
  });

  describe('Form Cleanup', () => {
    it('should clear form data after successful login', async () => {
      const user = userEvent.setup();
      mockOnSubmit.mockResolvedValue(mockSuccessResponse);
      
      render(<LoginForm onSubmit={mockOnSubmit} />);

      const usernameInput = screen.getByLabelText('Username') as HTMLInputElement;
      const passwordInput = screen.getByPlaceholderText('Enter your password') as HTMLInputElement;

      await user.type(usernameInput, 'testuser');
      await user.type(passwordInput, 'password123');
      await user.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(usernameInput.value).toBe('');
        expect(passwordInput.value).toBe('');
      });
    });
  });
});
