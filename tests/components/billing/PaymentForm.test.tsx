import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import * as React from 'react'
import PaymentForm from '@/components/billing/PaymentForm'

describe('PaymentForm', () => {
  it('validates amount and method and submits', async () => {
    const onSubmit = vi.fn().mockResolvedValue(undefined)
    render(<PaymentForm onSubmit={onSubmit} />)
    fireEvent.change(screen.getByLabelText('Amount'), { target: { value: '100' } })
    fireEvent.mouseDown(screen.getByLabelText('Payment method'))
    // open select and choose first option (Cash)
    const option = await screen.findByText('Cash')
    fireEvent.click(option)
    fireEvent.change(screen.getByLabelText('Payment date'), { target: { value: '2024-01-01' } })
    fireEvent.click(screen.getByRole('button', { name: /record payment/i }))
    await waitFor(() => expect(onSubmit).toHaveBeenCalled())
  })
})


