import { describe, it, expect } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import * as React from 'react'
import { DataTable, type ColumnDef } from '@/components/ui/DataTable'

interface Row { id: number; name: string; value: number }

const makeRows = (): Row[] => [
  { id: 1, name: '<PERSON>', value: 3 },
  { id: 2, name: '<PERSON>', value: 1 },
  { id: 3, name: '<PERSON>', value: 2 },
]

const columns: ColumnDef<Row>[] = [
  { id: 'name', header: 'Name', accessor: (r) => r.name, sortable: true },
  { id: 'value', header: 'Value', accessor: (r) => r.value, sortable: true },
]

describe('DataTable', () => {
  it('renders rows and headers', () => {
    render(<DataTable data={makeRows()} columns={columns} responsive={false} />)
    expect(screen.getByText('Name')).toBeInTheDocument()
    expect(screen.getByText('Value')).toBeInTheDocument()
    expect(screen.getByText('Alice')).toBeInTheDocument()
    expect(screen.getByText('Bob')).toBeInTheDocument()
  })

  it('sorts when header clicked', () => {
    render(<DataTable data={makeRows()} columns={columns} responsive={false} />)
    // Click on Value header to sort asc (default)
    fireEvent.click(screen.getByText('Value'))
    const cells = screen.getAllByRole('cell')
    // First row after sorting by value asc should contain 1 (Bob)
    expect(cells.some((c) => c.textContent === '1')).toBe(true)
  })

  it('supports column visibility toggles', () => {
    render(
      <DataTable
        data={makeRows()}
        columns={columns}
        responsive={false}
        showColumnControls
        initialVisibleColumnIds={[ 'name' ]}
      />
    )
    // Only Name column should be visible initially
    expect(screen.queryByText('Value')).not.toBeInTheDocument()
  })

  it('supports bulk selection', () => {
    render(
      <DataTable
        data={makeRows()}
        columns={columns}
        responsive={false}
        enableBulkSelection
      />
    )
    const checkboxes = screen.getAllByRole('checkbox')
    // First checkbox is "select all"
    fireEvent.click(checkboxes[0])
    // All row checkboxes should now be checked
    const rowChecks = screen.getAllByRole('checkbox').slice(1)
    expect(rowChecks.length).toBe(makeRows().length)
  })
})


