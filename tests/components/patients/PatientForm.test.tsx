import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import * as React from 'react'
import { PatientForm } from '@/components/patients/PatientForm'

describe('PatientForm', () => {
  it('validates required fields and submits', async () => {
    const onSubmit = vi.fn().mockResolvedValue(undefined)
    const onCancel = vi.fn()
    render(<PatientForm onSubmit={onSubmit} onCancel={onCancel} />)

    fireEvent.change(screen.getByLabelText('First Name'), { target: { value: 'John' } })
    fireEvent.change(screen.getByLabelText('Last Name'), { target: { value: 'Doe' } })
    fireEvent.change(screen.getByLabelText('Phone Number'), { target: { value: '+1 ************' } })
    fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } })
    fireEvent.change(screen.getByLabelText('Date of Birth'), { target: { value: '2000-01-01' } })

    fireEvent.click(screen.getByRole('button', { name: /save/i }))

    await waitFor(() => expect(onSubmit).toHaveBeenCalled())
  })
})


