import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import * as React from 'react'
import FindingForm from '@/components/clinical/FindingForm'

describe('FindingForm', () => {
  it('submits description and optional severity', async () => {
    const onSubmit = vi.fn().mockResolvedValue(undefined)
    render(<FindingForm onSubmit={onSubmit} />)
    fireEvent.change(screen.getByLabelText('Description'), { target: { value: 'Too<PERSON> has caries on occlusal surface' } })
    fireEvent.mouseDown(screen.getByLabelText('Severity (optional)'))
    const option = await screen.findByText('Medium')
    fireEvent.click(option)
    fireEvent.click(screen.getByRole('button', { name: /save finding/i }))
    await waitFor(() => expect(onSubmit).toHaveBeenCalled())
  })
})


