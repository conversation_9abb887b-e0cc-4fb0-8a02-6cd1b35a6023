import { describe, it, expect, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import * as React from 'react'

import { AppSidebar } from '@/components/layout/AppSidebar'
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar'

let mockPathname = '/dashboard'
vi.mock('next/navigation', () => ({
  usePathname: () => mockPathname,
}))

describe('AppSidebar navigation', () => {
  beforeEach(() => {
    // Reset path and cookies between tests
    mockPathname = '/dashboard'
    document.cookie = ''
  })

  it('renders links with dashboard-prefixed hrefs', () => {
    render(
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <SidebarTrigger />
        </SidebarInset>
      </SidebarProvider>
    )

    expect(screen.getByRole('link', { name: /dashboard/i }).getAttribute('href')).toBe('/dashboard')
    expect(screen.getByRole('link', { name: /patients/i }).getAttribute('href')).toBe('/dashboard/patients')
    expect(screen.getByRole('link', { name: /billing/i }).getAttribute('href')).toBe('/dashboard/billing')
    expect(screen.getByRole('link', { name: /reports/i }).getAttribute('href')).toBe('/dashboard/reports')
  })

  it('highlights active item based on current pathname', () => {
    mockPathname = '/dashboard/patients'
    render(
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <SidebarTrigger />
        </SidebarInset>
      </SidebarProvider>
    )

    const patientsLink = screen.getByRole('link', { name: /patients/i })
    expect(patientsLink).toHaveAttribute('data-active', 'true')
  })

  it('toggles collapsed state and sets cookie when trigger is clicked', async () => {
    const user = userEvent.setup()
    render(
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <SidebarTrigger />
        </SidebarInset>
      </SidebarProvider>
    )

    const sidebar = document.querySelector('[data-slot="sidebar"]') as HTMLElement | null
    const trigger = screen.getByRole('button', { name: /toggle sidebar/i })

    // Initial state should be expanded
    expect(sidebar).toBeTruthy()
    expect(sidebar!.getAttribute('data-state')).toBe('expanded')

    await user.click(trigger)

    // After toggle on desktop, state should be collapsed and cookie should be set
    expect(sidebar!.getAttribute('data-state')).toBe('collapsed')
    expect(document.cookie).toContain('sidebar_state=false')
  })
})


